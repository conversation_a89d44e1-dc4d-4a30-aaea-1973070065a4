/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
 */

package com.huawei.it.csrc.domain.service;

import com.huawei.it.csrc.application.dto.ImportTaskDTO;
import com.huawei.it.csrc.infrastructure.vo.AttachmenteExtVO;
import com.huawei.it.csrc.infrastructure.vo.ImportAndExportVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.Context;

/**
 * 根据导出任务ID获取下载链接
 *
 * @since 2022-08-19
 */
public interface IAttachmentDomainService {
    Map<Long, String> findRqDocByIds(List<Long> taskIds) throws ApplicationException;

    ImportTaskDTO findRqDocByImportId(Long taskId) throws ApplicationException;

    ImportTaskDTO findRqDocByExportId(Long taskId) throws ApplicationException;

    PagedResult<AttachmenteExtVO> findImportTaskList(ImportAndExportVO var1, PageVO var2) throws ApplicationException;

    PagedResult<AttachmenteExtVO> findExportTaskList(ImportAndExportVO var1, PageVO var2) throws ApplicationException;

    /**
     * 附件下载
     *
     * @param id       id
     * @param fileName fileName
     * @param response response
     * @return MessageResultDTO
     */
    void download(String id, String fileName, @Context HttpServletResponse response);
}