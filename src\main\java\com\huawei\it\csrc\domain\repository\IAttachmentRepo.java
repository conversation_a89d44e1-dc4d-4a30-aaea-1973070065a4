/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.domain.repository;

import com.huawei.it.csrc.domain.entity.ImportTaskEntity;
import com.huawei.it.csrc.infrastructure.vo.AttachmenteExtVO;
import com.huawei.it.csrc.infrastructure.vo.ImportAndExportVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import java.util.List;
import java.util.Map;

/**
 * 根据导出任务ID获取下载链接
 *
 * @since 2022-08-19
 */
public interface IAttachmentRepo {
    Map<Long, String> findRqDocByIds(List<Long> taskIds) throws ApplicationException;

    ImportTaskEntity findRqDocByImportId(Long taskId) throws ApplicationException;

    ImportTaskEntity findRqDocByExportId(Long taskId) throws ApplicationException;

    PagedResult<AttachmenteExtVO> findImportTaskList(ImportAndExportVO var1, PageVO var2) throws ApplicationException;

    PagedResult<AttachmenteExtVO> findExportTaskList(ImportAndExportVO var1, PageVO var2) throws ApplicationException;
}
