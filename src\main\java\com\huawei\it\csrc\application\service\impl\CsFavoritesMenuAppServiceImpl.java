/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.application.service.impl;

import com.huawei.it.csrc.application.assembler.CsFavoritesMenuConvertDtoAndEntity;
import com.huawei.it.csrc.application.dto.CsFavoritesMenuInputDTO;
import com.huawei.it.csrc.application.dto.CsFavoritesMenuOutputDTO;
import com.huawei.it.csrc.application.dto.CsFavoritesMenuSaveDTO;
import com.huawei.it.csrc.application.service.ICsFavoritesMenuAppService;
import com.huawei.it.csrc.domain.favorites.entity.CsFavoritesMenuEntity;
import com.huawei.it.csrc.domain.favorites.service.ICsFavoritesMenuDomainService;
import com.huawei.it.jalor5.core.annotation.JalorResource;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.inject.Inject;
import javax.inject.Named;

/**
 * 菜单收藏服务层实现
 *
 * <AUTHOR>
 * @since 2022-12-16 06:30:20
 */
@JalorResource(code = "CsFavoritesMenuAppServiceImpl", desc = "CsFavoritesMenuAppServiceImpl")
@Named
public class CsFavoritesMenuAppServiceImpl implements ICsFavoritesMenuAppService {
    @Inject
    private ICsFavoritesMenuDomainService csFavoritesMenuDomainService;
    @Inject
    private CsFavoritesMenuConvertDtoAndEntity csFavoritesMenuConvert;

    /**
     * 菜单收藏列表查询
     *
     * @param csFavoritesMenuInputDto csFavoritesMenuInputDto
     * @return List<CsFavoritesMenuOutputDTO>
     */
    @Override
    public List<CsFavoritesMenuOutputDTO> findByField(CsFavoritesMenuInputDTO csFavoritesMenuInputDto) {
        CsFavoritesMenuEntity inputEntity = csFavoritesMenuConvert.convertToEntity(csFavoritesMenuInputDto);
        List<CsFavoritesMenuEntity> listEntity = csFavoritesMenuDomainService.findByField(inputEntity);
        List<CsFavoritesMenuOutputDTO> result = new ArrayList<>();
        if (listEntity != null) {
            result = listEntity.stream().map((entity) -> {
                return csFavoritesMenuConvert.convertToDTO(entity);
            }).collect(Collectors.toList());
        }

        return result;
    }

    /**
     * 新增菜单收藏
     *
     * @param csFavoritesMenuSaveDto csFavoritesMenuSaveDto
     * @return void
     */
    @Override
    public void save(CsFavoritesMenuSaveDTO csFavoritesMenuSaveDto) {
        CsFavoritesMenuEntity inputEntity = csFavoritesMenuConvert.convertToEntity(csFavoritesMenuSaveDto);
        csFavoritesMenuDomainService.save(inputEntity);
    }
}
