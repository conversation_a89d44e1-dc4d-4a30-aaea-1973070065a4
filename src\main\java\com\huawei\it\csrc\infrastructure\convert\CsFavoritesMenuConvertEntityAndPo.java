/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.convert;

import com.huawei.it.csrc.domain.favorites.entity.CsFavoritesMenuEntity;
import com.huawei.it.csrc.infrastructure.po.CsFavoritesMenuPO;

import javax.inject.Named;

/**
 * The Convert Utils for CsFavoritesMenu
 *
 * <AUTHOR>
 * @since 2022-12-16 06:30:20
 */
@Named
public class CsFavoritesMenuConvertEntityAndPo {
    /**
     * 将PO转换为Entity
     *
     * @param csFavoritesMenuPo csFavoritesMenuPo
     * @return CsFavoritesMenuEntity
     */
    public CsFavoritesMenuEntity convertToEntity(CsFavoritesMenuPO csFavoritesMenuPo) {
        CsFavoritesMenuEntity csFavoritesMenuEntity = new CsFavoritesMenuEntity();
        if (csFavoritesMenuPo == null) {
            return csFavoritesMenuEntity;
        }
        csFavoritesMenuEntity.setSiteNodeId(csFavoritesMenuPo.getSiteNodeId());
        csFavoritesMenuEntity.setName(csFavoritesMenuPo.getName());
        csFavoritesMenuEntity.setUrl(csFavoritesMenuPo.getUrl());
        return csFavoritesMenuEntity;
    }

    /**
     * 将Entity转换为PO
     *
     * @param csFavoritesMenuEntity csFavoritesMenuEntity
     * @return CsFavoritesMenuPO
     */
    public CsFavoritesMenuPO convertToPO(CsFavoritesMenuEntity csFavoritesMenuEntity) {
        CsFavoritesMenuPO csFavoritesMenuPo = new CsFavoritesMenuPO();
        if (csFavoritesMenuEntity == null) {
            // 这里多是直接用于操作数据库的，不适合传null值
            return csFavoritesMenuPo;
        }
        csFavoritesMenuPo.setCreatedBy(csFavoritesMenuEntity.getCreatedBy());
        csFavoritesMenuPo.setSiteNodeId(csFavoritesMenuEntity.getSiteNodeId());
        csFavoritesMenuPo.setApplicationId(csFavoritesMenuEntity.getApplicationId());
        csFavoritesMenuPo.setTenantId(csFavoritesMenuEntity.getTenantId());
        return csFavoritesMenuPo;
    }
}
