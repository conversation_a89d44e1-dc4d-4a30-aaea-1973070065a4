/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
 */

package com.huawei.it.csrc.interfaces.facade;

import com.huawei.it.csrc.base.vo.MessageResultVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * 功能描述：进行中任务权限校验
 *
 * @since 2022-08-24
 */
@Path("/functionSetFacade")
@Produces(MediaType.APPLICATION_JSON)
public interface IFunctionSetFacade {
    /**
     * 功能描述：进行中任务权限校验
     *
     * @param userId 用户ID
     * @param functionName 功能集名称
     * @return LookupItemVO
     * @throws ApplicationException
     */
    @GET
    @Path("/getFunctionCount/{userId}/{functionName}")
    MessageResultVO<Integer> getFunctionCount(@PathParam("userId") long userId,
        @PathParam("functionName") String functionName) throws ApplicationException;
}
