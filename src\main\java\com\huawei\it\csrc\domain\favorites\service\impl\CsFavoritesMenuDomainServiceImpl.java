/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.domain.favorites.service.impl;

import com.huawei.it.csrc.domain.favorites.entity.CsFavoritesMenuEntity;
import com.huawei.it.csrc.domain.favorites.repository.ICsFavoritesMenuRepo;
import com.huawei.it.csrc.domain.favorites.service.ICsFavoritesMenuDomainService;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

/**
 * 菜单收藏领域层实现
 *
 * <AUTHOR>
 * @since 2022-12-16 06:30:20
 */
@Named
public class CsFavoritesMenuDomainServiceImpl implements ICsFavoritesMenuDomainService {
    @Inject
    private ICsFavoritesMenuRepo csFavoritesMenuRepo;

    /**
     * 菜单收藏列表查询
     *
     * @param csFavoritesMenuEntity csFavoritesMenuEntity
     * @return List<CsFavoritesMenuEntity>
     */
    @Override
    public List<CsFavoritesMenuEntity> findByField(CsFavoritesMenuEntity csFavoritesMenuEntity) {
        return csFavoritesMenuRepo.findByField(csFavoritesMenuEntity);
    }

    /**
     * 新增菜单收藏
     *
     * @param csFavoritesMenuEntity csFavoritesMenuEntity
     * @return void
     */
    @Override
    public void save(CsFavoritesMenuEntity csFavoritesMenuEntity) {
        csFavoritesMenuRepo.save(csFavoritesMenuEntity);
    }
}
