/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.application.dto;

import com.huawei.it.csrc.infrastructure.exception.CsrcServiceException;

import lombok.Data;

@Data
public class MessageResultDTO<T> {
    private String code = "200";
    private String msg = "success";
    private T data;

    public MessageResultDTO() {
    }

    public MessageResultDTO(String code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static <T> MessageResultDTO<T> success(T data) {
        MessageResultDTO<T> responseVo = new MessageResultDTO();
        responseVo.setCode("200");
        responseVo.setMsg("ok");
        responseVo.setData(data);
        return responseVo;
    }

    public static <T> MessageResultDTO<T> success() {
        MessageResultDTO<T> responseVo = new MessageResultDTO();
        responseVo.setCode("200");
        responseVo.setMsg("ok");
        return responseVo;
    }

    public static <T> MessageResultDTO<T> failure(CsrcServiceException ex) {
        MessageResultDTO<T> responseDto = new MessageResultDTO();
        responseDto.setCode(ex.getErrCode());
        responseDto.setMsg(ex.getMessage());
        return responseDto;
    }
}
