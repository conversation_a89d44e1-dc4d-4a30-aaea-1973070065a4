/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.interfaces.facade.impl;

import com.huawei.it.csrc.application.dto.CsFavoritesMenuInputDTO;
import com.huawei.it.csrc.application.dto.CsFavoritesMenuOutputDTO;
import com.huawei.it.csrc.application.dto.CsFavoritesMenuSaveDTO;
import com.huawei.it.csrc.application.dto.MessageResultDTO;
import com.huawei.it.csrc.application.service.ICsFavoritesMenuAppService;
import com.huawei.it.csrc.interfaces.facade.ICsFavoritesMenuFacade;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.request.RequestConstants;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

/**
 * 菜单收藏接口
 *
 * <AUTHOR>
 * @since 2022-12-16 06:30:20
 */
@Named
@JalorResource(code = "CsFavoritesMenuFacadeImpl", desc = "Cs Favorites Menu Facade Impl")
public class CsFavoritesMenuFacadeImpl implements ICsFavoritesMenuFacade {
    @Inject
    private ICsFavoritesMenuAppService csFavoritesMenuService;

    /**
     * 菜单收藏列表查询
     *
     * @param csFavoritesMenuInputDto csFavoritesMenuInputDto
     * @return MessageResultDTO<List < CsFavoritesMenuOutputDTO>>
     */
    @Override
    @Audit
    @JalorOperation(code = RequestConstants.READ, desc = RequestConstants.READ_DESC)
    public MessageResultDTO<List<CsFavoritesMenuOutputDTO>> findByField(
        CsFavoritesMenuInputDTO csFavoritesMenuInputDto) {
        List<CsFavoritesMenuOutputDTO> resultList = csFavoritesMenuService.findByField(csFavoritesMenuInputDto);
        return MessageResultDTO.success(resultList);
    }

    /**
     * 新增菜单收藏
     *
     * @param csFavoritesMenuSaveDto csFavoritesMenuSaveDto
     * @return MessageResultDTO
     */
    @Override
    @Audit
    @JalorOperation(code = RequestConstants.READ, desc = RequestConstants.READ_DESC)
    public MessageResultDTO save(CsFavoritesMenuSaveDTO csFavoritesMenuSaveDto) {
        csFavoritesMenuService.save(csFavoritesMenuSaveDto);
        return MessageResultDTO.success();
    }
}
