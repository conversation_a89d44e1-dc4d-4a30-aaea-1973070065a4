/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.dao;

import com.huawei.it.csrc.domain.entity.ImportTaskEntity;
import com.huawei.it.csrc.infrastructure.vo.AttachmenteExtVO;
import com.huawei.it.csrc.infrastructure.vo.ImportAndExportVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

/**
 * 功能描述：附件查询DAO
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
public interface IAttachmentExDao {
    List<Map<String, Object>> findRqDocByIds(@Param("taskIds") List<Long> taskids);

    ImportTaskEntity findRqDocByImportId(@Param("taskId") Long taskId);

    ImportTaskEntity findRqDocByExportId(@Param("taskId") Long taskId);

    PagedResult<AttachmenteExtVO> findImportTaskList(ImportAndExportVO var1, PageVO var2) throws ApplicationException;

    PagedResult<AttachmenteExtVO> findExportTaskList(ImportAndExportVO var1, PageVO var2) throws ApplicationException;
}
