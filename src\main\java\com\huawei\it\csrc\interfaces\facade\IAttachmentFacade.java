/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
 */

package com.huawei.it.csrc.interfaces.facade;

import com.huawei.it.csrc.application.dto.ImportTaskDTO;
import com.huawei.it.csrc.base.vo.MessageResultVO;
import com.huawei.it.csrc.infrastructure.vo.AttachmenteExtVO;
import com.huawei.it.csrc.infrastructure.vo.ImportAndExportVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.RequestBody;

import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * 根据导出任务ID获取下载链接
 *
 * @since 2022-08-19
 */
@Path("/attachment")
@Produces(MediaType.APPLICATION_JSON)
public interface IAttachmentFacade {
    /**
     * 根据导出任务ID获取下载链接
     *
     * @param taskIds 任务ID列表
     * @return MessageResultVO
     * @throws ApplicationException
     */
    @POST
    @Path("/findRqDocById")
    MessageResultVO<Map<Long, String>> findRqDocByIds(List<Long> taskIds) throws ApplicationException;

    /**
     * 根据导入任务ID获取下载链接
     *
     * @param taskId 任务ID
     * @return MessageResultVO
     * @throws ApplicationException
     */
    @POST
    @Path("/findRqDocByImportId")
    MessageResultVO<ImportTaskDTO> findRqDocByImportId(Long taskId) throws ApplicationException;

    /**
     * 根据导入任务ID获取下载链接
     *
     * @param taskId 任务ID
     * @return MessageResultVO
     * @throws ApplicationException
     */
    @POST
    @Path("/findRqDocByExportId")
    MessageResultVO<ImportTaskDTO> findRqDocByExportId(Long taskId) throws ApplicationException;

    /**
     * 个人中心-我的导入导出
     *
     * @param importAndExportVO importAndExportVO
     * @param pageVO            分页信息
     * @return PagedResult
     * @throws ApplicationException
     */
    @POST
    @Path("/myImportAndExportList/{curPage}/{pageSize}")
    PagedResult<AttachmenteExtVO> myImportAndExportList(@RequestBody ImportAndExportVO importAndExportVO,
        @PathParam("") PageVO pageVO) throws ApplicationException;

    /**
     * 附件下载
     *
     * @param taskId           taskId
     * @param importExportFlag importExportFlag
     * @return MessageResultDTO
     * @throws ApplicationException
     */
    @GET
    @Path("/download/{taskId}/{importExportFlag}")
    MessageResultVO<Map<String, String>> download(@PathParam("taskId") Long taskId,
        @PathParam("importExportFlag") String importExportFlag) throws ApplicationException;
}
