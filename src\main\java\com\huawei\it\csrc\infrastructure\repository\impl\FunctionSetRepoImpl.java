/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.repository.impl;

import com.huawei.it.csrc.domain.repository.IFunctionSetRepo;
import com.huawei.it.csrc.infrastructure.dao.IFunctionSetExDao;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import javax.annotation.Resource;
import javax.inject.Named;

/**
 * 功能描述：进行中任务权限校验
 *
 * @since 2022-08-24
 */
@Named
public class FunctionSetRepoImpl implements IFunctionSetRepo {
    @Resource
    private IFunctionSetExDao functionSetExDao;

    @Override
    public int getFunctionCount(long userId, String functionName) throws ApplicationException {
        return functionSetExDao.getFunctionCount(userId, functionName);
    }
}
