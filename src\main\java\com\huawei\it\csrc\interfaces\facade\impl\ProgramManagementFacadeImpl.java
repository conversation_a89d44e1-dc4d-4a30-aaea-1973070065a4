/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.interfaces.facade.impl;

import com.huawei.it.csrc.application.dto.response.BaseResponse;
import com.huawei.it.csrc.application.service.IProgramManagementAppService;
import com.huawei.it.csrc.interfaces.facade.IProgramManagementFacade;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.security.ProgramVO;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.IOException;


/**
 * 查找或创建数据范围
 *
 * <AUTHOR>
 * @since 2024-06-27 16:18
 */
@Named
@JalorResource(code = "IProgramManagementFacadeImpl", desc = "IProgramManagementFacadeImpl")
public class ProgramManagementFacadeImpl implements IProgramManagementFacade {

    @Inject
    private IProgramManagementAppService iProgramManagementAppService;

    @JalorOperation(code = "findOrCreateProgram", desc = "findOrCreateProgram")
    @Override
    public BaseResponse<ProgramVO> findOrCreateProgram(String programName, String scope) throws ApplicationException, IOException {
        ProgramVO programVO = iProgramManagementAppService.findOrCreateProgram(programName, scope);
        return BaseResponse.success(programVO);
    }
}

