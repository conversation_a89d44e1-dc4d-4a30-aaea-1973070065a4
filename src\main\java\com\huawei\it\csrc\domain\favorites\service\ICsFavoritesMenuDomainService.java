/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.domain.favorites.service;

import com.huawei.it.csrc.domain.favorites.entity.CsFavoritesMenuEntity;

import java.util.List;

/**
 * 菜单收藏领域层
 *
 * <AUTHOR>
 * @since 2022-12-16 06:30:20
 */
public interface ICsFavoritesMenuDomainService {
    /**
     * 菜单收藏列表查询
     *
     * @param csFavoritesMenuEntity csFavoritesMenuEntity
     * @return List<CsFavoritesMenuEntity>
     */
    List<CsFavoritesMenuEntity> findByField(CsFavoritesMenuEntity csFavoritesMenuEntity);

    /**
     * 菜单收藏新增
     *
     * @param csFavoritesMenuEntity csFavoritesMenuEntity
     * @return void
     */
    void save(CsFavoritesMenuEntity csFavoritesMenuEntity);
}
