/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.application.service;

import com.huawei.it.jalor5.core.request.impl.Application;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;


import org.junit.AfterClass;
import org.junit.BeforeClass;

import java.util.HashMap;
import java.util.Map;

/**
 * BaseTest 单元测试的基类
 * 需要在此构建上下文，用户等
 *
 * <AUTHOR>
 * @since 2021 -07-09
 */
public abstract class BaseTest {
    /**
     * 构建上下文
     */
    @BeforeClass
    public static void init() {
        RequestContext requestContext = new RequestContext();
        Application application = new Application();
        application.setAppName("opt_fsp_cs");
        requestContext.setApplication(application);
        requestContext.setUserLanguage("en_US");
        requestContext.setUserIp("***************");
        setCurrentUser(requestContext);
        Map<String, Object> map = new HashMap<>();
        map.put("JalorCookiesType", "hic_lang=zh_CN");
        requestContext.setItems(map);
        // 忽略权限检查
        requestContext.setSkipSecurityCheck(true);
        RequestContextManager.setCurrent(requestContext);
    }

    private static void setCurrentUser(RequestContext requestContext) {
        UserVO user = new UserVO();
        user.setUserId(131269511L);
        user.setUserAccount("test1");
        user.setUserCN("test1");
        requestContext.setUser(user);
    }

    /**
     * After.
     */
    @AfterClass
    public static void after() {
    }
}
