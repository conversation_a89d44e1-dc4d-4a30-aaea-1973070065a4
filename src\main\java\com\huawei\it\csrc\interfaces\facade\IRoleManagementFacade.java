/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.interfaces.facade;

import com.huawei.it.csrc.application.dto.response.BaseResponse;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.security.RoleVO;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import java.io.IOException;

/**
 * 作业岛角色查询创建
 *
 * <AUTHOR>
 * @since 2024-04-24 09:52
 */
@Path("/v1/roleManagement")
@Produces(MediaType.APPLICATION_JSON)
public interface IRoleManagementFacade {
    @GET
    @Path("/findOrCreateRole")
    BaseResponse<RoleVO> findOrCreateRole(@QueryParam("roleName") String roleName, @QueryParam("scope")String scope) throws ApplicationException, IOException;
}
