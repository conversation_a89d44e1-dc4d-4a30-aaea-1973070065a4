<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jaxrs="http://cxf.apache.org/jaxrs" xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd       http://cxf.apache.org/jaxrs http://cxf.apache.org/schemas/jaxrs.xsd">
    <jaxrs:server id="commServiceRest" address="/">
        <jaxrs:serviceBeans>
            <ref bean="attachmentFacadeImpl" />
            <ref bean="functionSetFacadeImpl" />
            <ref bean="csRecentlyUsedFacadeImpl" />
            <ref bean="csFavoritesMenuFacadeImpl"/>
            <ref bean="roleManagementFacadeImpl"/>
            <ref bean="programManagementFacadeImpl"/>
        </jaxrs:serviceBeans>
        <jaxrs:providers>
            <ref bean="jsonProvider" />
        </jaxrs:providers>
    </jaxrs:server>
</beans>

