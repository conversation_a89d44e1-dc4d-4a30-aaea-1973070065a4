/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.domain.service.impl;

import com.huawei.it.csrc.domain.repository.IRolePermissionAndSiteNodeRepo;
import com.huawei.it.csrc.domain.service.IRoleManagementDomainService;
import com.huawei.it.csrc.infrastructure.po.AclentryPO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.request.IUserPrincipal;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.util.JsonUtil;
import com.huawei.it.jalor5.core.util.exception.NoDataFoundException;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.service.IRoleService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.inject.Inject;
import javax.inject.Named;
import java.io.IOException;
import java.util.List;

/**
 * 查找或复制角色，并复制权限点
 *
 * <AUTHOR>
 * @since 2024-04-24 10:00
 */
@Named
public class RoleManagementDomainService implements IRoleManagementDomainService {

    @Inject
    private IRoleService roleService;
    @Resource
    private IRolePermissionAndSiteNodeRepo iRolePermissionAndSiteNodeRepo;

    @Value("${standardScope:opt_fsp_pub_service}")
    private String standardScope;


    /**
     * 查找或复制角色，并复制权限点
     *
     * @param queryRole
     * @return
     * @throws ApplicationException
     * @throws IOException
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRES_NEW)
    public RoleVO findOrCreateRole(RoleVO queryRole) throws ApplicationException, IOException {

        RoleVO roleVO;
        RoleVO standardRole;
        try {
            roleVO = roleService.findRole(queryRole);
        } catch (NoDataFoundException exception) {
            RoleVO standardRoleQuery = new RoleVO();

            standardRoleQuery.setRoleName(queryRole.getRoleName());
            standardRoleQuery.setScope(standardScope);
            // roleId=0才会触发scope查询
            standardRoleQuery.setRoleId(0);
            standardRoleQuery.setTenantId("-1");
            standardRole = roleService.findRole(standardRoleQuery);
            standardRole.setScope(queryRole.getScope());

            // 深拷贝角色信息，因为createRole会修改入参
            String objectToJson = JsonUtil.objectToJson(standardRole);
            RoleVO createRoleVO = JsonUtil.stringToObject(objectToJson, RoleVO.class);
            roleService.createRole(createRoleVO);
            roleVO = roleService.findRole(queryRole);
            copyPermissionByRoleId(standardRole.getRoleId(), roleVO.getRoleId());
        }

        return roleVO;
    }


    /**
     * 复制标准角色的权限点和栏目
     *
     * @param sourceRoleId
     * @param targetRoleId
     * @return
     */
    private Integer copyPermissionByRoleId(Integer sourceRoleId, Integer targetRoleId) {
        List<AclentryPO> aclentryPOList = iRolePermissionAndSiteNodeRepo.findPermissionAndSiteNodeIdByRoleId(sourceRoleId);
        IUserPrincipal userVO = RequestContext.getCurrent().getUser();
        for (AclentryPO aclentryPO : aclentryPOList) {
            aclentryPO.setCreatedBy(userVO.getUserId());
            aclentryPO.setRoleId(targetRoleId);
        }
        return iRolePermissionAndSiteNodeRepo.insertPermissionAndSiteNodeId(aclentryPOList);
    }
}
