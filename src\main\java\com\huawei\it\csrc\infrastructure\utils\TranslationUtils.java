/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * 栏目名称翻译类
 *
 * <AUTHOR>
 * @since 2022-12-19 15:06:00
 */
public class TranslationUtils {
    private static final String COMMA = ",";

    private static final String EQUALS_SIGN = "=";

    /**
     * 翻译栏目名称
     *
     * @param language 语言
     * @param name     名称
     * @return String
     */
    public static String translationName(String language, String name) {
        String realName = "";
        if (StringUtils.isNotEmpty(name)) {
            String[] nameKeys = name.split(COMMA);
            for (String nameKey : nameKeys) {
                String[] langeKeys = nameKey.split(EQUALS_SIGN);
                if (StringUtils.equals(language, langeKeys[0]) && langeKeys.length > 1) {
                    realName = langeKeys[1];
                    break;
                }
            }
        }
        return realName;
    }
}
