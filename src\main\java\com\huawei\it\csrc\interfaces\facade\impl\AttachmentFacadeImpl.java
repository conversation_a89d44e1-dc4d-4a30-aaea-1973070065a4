/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
 */

package com.huawei.it.csrc.interfaces.facade.impl;

import com.huawei.it.csrc.application.dto.ImportTaskDTO;
import com.huawei.it.csrc.application.service.IAttachmentAppService;
import com.huawei.it.csrc.base.vo.MessageResultVO;
import com.huawei.it.csrc.infrastructure.vo.AttachmenteExtVO;
import com.huawei.it.csrc.infrastructure.vo.ImportAndExportVO;
import com.huawei.it.csrc.interfaces.facade.IAttachmentFacade;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.request.IUserPrincipal;
import com.huawei.it.jalor5.core.request.RequestConstants;
import com.huawei.it.jalor5.core.request.RequestContextException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import javax.inject.Named;

/**
 * 根据导出任务ID获取下载链接
 *
 * @since 2022-08-19
 */
@Named
@JalorResource(code = "AttachmentFacadeImpl", desc = "Attachment Facade Impl")
public class AttachmentFacadeImpl implements IAttachmentFacade {
    private static final String IMPORT_FLAG = "1";

    private static final String EXPORT_FLAG = "2";

    private static final String EXPORT_STATUS_TWO = "2";

    private static final String EXPORT_STATUS_THREE = "3";

    private static final String IMPORT_STATUS_THREE = "3";

    private static final String EXPORT_STATUS_FOUR = "4";

    private static final String EXPORT_URL = "filePath";

    private static final String EXPORT_FILENAME = "fileName";

    private static final String S3_ID = "_s3_"; // s3标识

    private static final String ERROR_INFO = "error_info.txt";

    @Resource
    IAttachmentAppService appService;

    /**
     * 查询ByIds
     *
     * @param taskIds taskIds
     * @return MessageResultVO
     * @throws ApplicationException
     */
    @Override
    @JalorOperation(code = RequestConstants.READ, desc = RequestConstants.READ_DESC)
    @Audit(module = "Program Management", operation = "attachment.findRqDocByIds")
    public MessageResultVO<Map<Long, String>> findRqDocByIds(List<Long> taskIds) throws ApplicationException {
        return appService.findRqDocByIds(taskIds);
    }

    /**
     * 查询ByImportId
     *
     * @param taskId taskId
     * @return MessageResultVO
     * @throws ApplicationException
     */
    @Override
    @JalorOperation(code = RequestConstants.READ, desc = RequestConstants.READ_DESC)
    @Audit(module = "Program Management", operation = "attachment.findRqDocByImportId")
    public MessageResultVO<ImportTaskDTO> findRqDocByImportId(Long taskId) throws ApplicationException {
        return appService.findRqDocByImportId(taskId);
    }

    /**
     * 查询ByExportId
     *
     * @param taskId taskId
     * @return MessageResultVO
     * @throws ApplicationException
     */
    @Override
    @JalorOperation(code = RequestConstants.READ, desc = RequestConstants.READ_DESC)
    @Audit(module = "Program Management", operation = "attachment.findRqDocByExportId")
    public MessageResultVO<ImportTaskDTO> findRqDocByExportId(Long taskId) throws ApplicationException {
        return appService.findRqDocByExportId(taskId);
    }

    /**
     * 导入列表
     *
     * @param importAndExportVO importAndExportVO
     * @param pageVO pageVO
     * @return PagedResult
     * @throws ApplicationException
     */
    @Override
    @JalorOperation(code = RequestConstants.READ, desc = RequestConstants.READ_DESC)
    @Audit(module = "Program Management", operation = "attachment.myImportAndExportList")
    public PagedResult<AttachmenteExtVO> myImportAndExportList(ImportAndExportVO importAndExportVO, PageVO pageVO)
        throws ApplicationException {
        String importExportFlag = importAndExportVO.getImportExportFlag();
        if (StringUtils.isEmpty(importExportFlag)) {
            throw new RequestContextException();
        }
        IUserPrincipal user = RequestContext.getCurrent().getUser();
        if (user == null) {
            throw new RequestContextException();
        }
        importAndExportVO.setCurrentUserId(user.getUserId());
        importAndExportVO.setLanguage(RequestContext.getCurrent().getUserLanguage());
        PagedResult<AttachmenteExtVO> pagedResult;
        if (IMPORT_FLAG.equals(importExportFlag)) {
            pagedResult = appService.findImportTaskList(importAndExportVO, pageVO);
            // 翻译成功失败进行中
            if (pagedResult != null && pagedResult.getResult() != null) {
                translationStatus(pagedResult.getResult());
            }
        } else if (EXPORT_FLAG.equals(importExportFlag)) {
            pagedResult = appService.findExportTaskList(importAndExportVO, pageVO);
            // 翻译成功失败进行中
            if (pagedResult != null && pagedResult.getResult() != null) {
                translationStatus(pagedResult.getResult());
            }
        } else {
            pagedResult = new PagedResult<>();
            pagedResult.setPageVO(pageVO);
            pagedResult.setResult(new ArrayList<>());
        }
        return pagedResult;
    }

    private void translationStatus(List<AttachmenteExtVO> voList) {
        for (int i = 0; i < voList.size(); i++) {
            AttachmenteExtVO vo = voList.get(i);
            // 导出
            if (StringUtils.equals(EXPORT_STATUS_TWO, vo.getImportExportFlag())) {
                exportStatus(vo);
            } else {
                importStatus(vo);
            }

            // 计算文件大小字节转换为KB
            if (vo.getFileSize() != null) {
                BigDecimal fileSize = BigDecimal.valueOf(vo.getFileSize());
                BigDecimal divisor = BigDecimal.valueOf(1024);
                double kbFileSize = fileSize.divide(divisor, 2, BigDecimal.ROUND_HALF_UP).doubleValue();
                vo.setFileSize(kbFileSize);
            }
            // 计算执行任务耗时，单位秒
            calculationTime(vo);
        }
    }

    private void importStatus(AttachmenteExtVO vo) {
        if (StringUtils.equals(EXPORT_STATUS_FOUR, vo.getImportAndExportStatus())) {
            // 3 失败
            vo.setImportAndExportStatus(EXPORT_STATUS_THREE);
            // 失败的情况，如果有errorMsg，则显示文件名字为errorMsg
            if (StringUtils.isNotEmpty(vo.getErrorMsg())) {
                vo.setFileName(vo.getErrorMsg());
            }
        } else if (StringUtils.equals(EXPORT_STATUS_THREE, vo.getImportAndExportStatus())) {
            // 4 部分成功
            vo.setImportAndExportStatus(EXPORT_STATUS_FOUR);
        }
    }

    private void exportStatus(AttachmenteExtVO vo) {
        if (StringUtils.equals(EXPORT_STATUS_THREE, vo.getImportAndExportStatus())) {
            // 3 失败
            // 失败的情况，如果有errorMsg，则显示文件名字为errorMsg
            if (StringUtils.isNotEmpty(vo.getErrorMsg())) {
                vo.setFileName(vo.getErrorMsg());
            }
        }
    }

    private void calculationTime(AttachmenteExtVO vo) {
        // 处理中不计算
        if (StringUtils.equals(EXPORT_FLAG, vo.getImportExportFlag())) {
            if (!StringUtils.equals(EXPORT_STATUS_TWO, vo.getImportAndExportStatus()) && !StringUtils.equals(
                IMPORT_STATUS_THREE, vo.getImportAndExportStatus())) {
                return;
            }
        } else {
            if (!StringUtils.equals(EXPORT_STATUS_TWO, vo.getImportAndExportStatus()) && !StringUtils.equals(
                IMPORT_STATUS_THREE, vo.getImportAndExportStatus()) && !StringUtils.equals(EXPORT_STATUS_FOUR,
                vo.getImportAndExportStatus())) {
                return;
            }
        }
        // 开始时间或者结束时间有一个为空，也不计算
        if (vo.getRunStartTime() == null || vo.getRunEndTime() == null) {
            return;
        }

        // 计算开始结束时间相差秒数
        long timeTaken = (vo.getRunEndTime().getTime() - vo.getRunStartTime().getTime()) / 1000;
        vo.setTimeTaken(timeTaken);
    }

    /**
     * 下载
     *
     * @param taskId taskId
     * @param importExportFlag importExportFlag
     * @return MessageResultVO
     * @throws ApplicationException
     */
    @Override
    @JalorOperation(code = RequestConstants.READ, desc = RequestConstants.READ_DESC)
    @Audit(module = "Program Management", operation = "attachment.download")
    public MessageResultVO<Map<String, String>> download(Long taskId, String importExportFlag)
        throws ApplicationException {
        Map<String, String> backMap = new HashMap<>();
        if (EXPORT_FLAG.equals(importExportFlag)) {
            MessageResultVO<ImportTaskDTO> exportVO = this.findRqDocByExportId(taskId);
            if (exportVO != null && exportVO.getData() != null) {
                backMap.put(EXPORT_URL, exportVO.getData().getFilePath());
                backMap.put(EXPORT_FILENAME, exportVO.getData().getFileName());
            }
        } else if (IMPORT_FLAG.equals(importExportFlag)) {
            MessageResultVO<ImportTaskDTO> importVO = this.findRqDocByImportId(taskId);
            if (importVO != null && importVO.getData() != null) {
                if (StringUtils.isNotEmpty(importVO.getData().getRemark())
                    && importVO.getData().getRemark().indexOf(S3_ID) > -1) {
                    backMap.put(EXPORT_URL, importVO.getData().getRemark());
                    backMap.put(EXPORT_FILENAME,
                        importVO.getData().getRemark().endsWith(".txt") ? ERROR_INFO : importVO.getData().getFileName());
                } else {
                    backMap.put(EXPORT_URL, importVO.getData().getFilePath());
                    backMap.put(EXPORT_FILENAME, importVO.getData().getFileName());
                }
            }
        }
        return MessageResultVO.success(backMap);
    }
}
