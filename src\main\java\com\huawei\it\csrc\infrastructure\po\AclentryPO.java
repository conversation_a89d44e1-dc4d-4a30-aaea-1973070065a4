/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.po;

import lombok.Data;

import java.util.Date;

/**
 * 权限点和栏目
 *
 * <AUTHOR>
 * @since 2024-04-24 11:40
 */
@Data
public class AclentryPO {


    /**
     * 主键ID
     */
    Integer id;

    /**
     * 区分表示权限资源类型，Service表示功能权限点，sitMap表示栏目
     */
    String resourceType;

    /**
     * 栏目ID
     */
    Long siteNodeId;

    /**
     * 角色ID
     */
    Integer roleId;

    /**
     * 权限点ID
     */
    Integer permissionId;

    /**
     * 创建人user Id，对应 tpl_user_t 的 user_id 字段
     */
    private Long createdBy;

    /**
     * 创建时间，对应 creation_date 字段
     */
    private Date creationDate;

    /**
     * 创建人 CN 名，如 lijiaxiang 54883，对应 tpl_user_t 的 lname
     * 字段，此字段用于显示在界面上（显示CN名比W3账号更友好）
     */
    private String creationUserCN;

    /**
     * 最后更新时间，对应 last_update_date 字段
     */
    private Date lastUpdateDate;

}
