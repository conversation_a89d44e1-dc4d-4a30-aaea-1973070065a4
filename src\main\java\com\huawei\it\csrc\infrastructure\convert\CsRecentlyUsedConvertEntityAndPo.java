/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.convert;

import com.huawei.it.csrc.domain.entity.CsRecentlyUsedEntity;
import com.huawei.it.csrc.infrastructure.po.CsRecentlyUsedPO;

import javax.inject.Named;

/**
 * The Convert Utils for CsRecentlyUsed
 *
 * <AUTHOR>
 * @since 2022-12-13 10:19:04
 */
@Named
public class CsRecentlyUsedConvertEntityAndPo {
    /**
     * 将PO转换为Entity
     *
     * @param csRecentlyUsedPo csRecentlyUsedPo
     * @return CsRecentlyUsedEntity
     */
    public CsRecentlyUsedEntity convertToEntity(CsRecentlyUsedPO csRecentlyUsedPo) {
        CsRecentlyUsedEntity csRecentlyUsedEntity = new CsRecentlyUsedEntity();
        if (csRecentlyUsedPo == null) {
            return csRecentlyUsedEntity;
        }
        csRecentlyUsedEntity.setSiteNodeId(csRecentlyUsedPo.getSiteNodeId());
        csRecentlyUsedEntity.setName(csRecentlyUsedPo.getName());
        csRecentlyUsedEntity.setUrl(csRecentlyUsedPo.getUrl());
        return csRecentlyUsedEntity;
    }

    /**
     * 将Entity转换为PO
     *
     * @param csRecentlyUsedEntity csRecentlyUsedEntity
     * @return CsRecentlyUsedPO
     */
    public CsRecentlyUsedPO convertToPO(CsRecentlyUsedEntity csRecentlyUsedEntity) {
        CsRecentlyUsedPO csRecentlyUsedPo = new CsRecentlyUsedPO();
        if (csRecentlyUsedEntity == null) {
            return csRecentlyUsedPo;
        }
        csRecentlyUsedPo.setCreatedBy(csRecentlyUsedEntity.getCreatedBy());
        csRecentlyUsedPo.setSiteNodeId(csRecentlyUsedEntity.getSiteNodeId());
        csRecentlyUsedPo.setApplicationId(csRecentlyUsedEntity.getApplicationId());
        csRecentlyUsedPo.setTenantId(csRecentlyUsedEntity.getTenantId());
        return csRecentlyUsedPo;
    }
}
