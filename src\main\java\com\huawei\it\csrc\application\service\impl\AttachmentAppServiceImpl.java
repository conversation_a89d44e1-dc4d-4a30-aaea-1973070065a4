/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.application.service.impl;

import com.huawei.it.csrc.application.dto.ImportTaskDTO;
import com.huawei.it.csrc.application.service.IAttachmentAppService;
import com.huawei.it.csrc.base.vo.MessageResultVO;
import com.huawei.it.csrc.domain.service.IAttachmentDomainService;
import com.huawei.it.csrc.infrastructure.vo.AttachmenteExtVO;
import com.huawei.it.csrc.infrastructure.vo.ImportAndExportVO;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import java.util.List;
import java.util.Map;

import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;

/**
 * 根据导出任务ID获取下载链接
 *
 * @since 2022-08-19
 */
@JalorResource(code = "AttachmentAppServiceImpl", desc = "AttachmentAppServiceImpl")
@Named
public class AttachmentAppServiceImpl implements IAttachmentAppService {
    @Inject
    private IAttachmentDomainService domainService;

    @Override
    public MessageResultVO<Map<Long, String>> findRqDocByIds(List<Long> taskIds) throws ApplicationException {
        return MessageResultVO.success(domainService.findRqDocByIds(taskIds));
    }

    @Override
    public MessageResultVO<ImportTaskDTO> findRqDocByImportId(Long taskId) throws ApplicationException {
        return MessageResultVO.success(domainService.findRqDocByImportId(taskId));
    }

    @Override
    public MessageResultVO<ImportTaskDTO> findRqDocByExportId(Long taskId) throws ApplicationException {
        return MessageResultVO.success(domainService.findRqDocByExportId(taskId));
    }

    @Override
    public PagedResult<AttachmenteExtVO> findImportTaskList(ImportAndExportVO var1, PageVO var2)
        throws ApplicationException {
        return domainService.findImportTaskList(var1, var2);
    }

    @Override
    public PagedResult<AttachmenteExtVO> findExportTaskList(ImportAndExportVO var1, PageVO var2)
        throws ApplicationException {
        return domainService.findExportTaskList(var1, var2);
    }

    /**
     * 根据id下载附件
     *
     * @param id       id
     * @param fileName fileName
     * @param response response
     * @return MessageResultDTO
     */
    @Override
    public MessageResultVO download(String id, String fileName, HttpServletResponse response) {
        domainService.download(id, fileName, response);
        return MessageResultVO.success();
    }
}
