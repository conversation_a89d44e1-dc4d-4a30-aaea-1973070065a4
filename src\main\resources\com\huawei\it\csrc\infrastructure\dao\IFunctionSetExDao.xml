<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.csrc.infrastructure.dao.IFunctionSetExDao">
    <select id="getFunctionCount" resultType="integer">
        SELECT
        count(1)
        FROM
        tpl_user_role_program_t T,
        tpl_tenant_role_acl_t d,
        tpl_function_t M
        WHERE
        T.role_id = d.role_id
        AND d.function_id = M.function_id
        and t.user_id=#{userId,jdbcType=NUMERIC}
        and m.function_name=#{functionName,jdbcType=VARCHAR}
    </select>
</mapper>