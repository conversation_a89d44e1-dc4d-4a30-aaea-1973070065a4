/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.application.service;

import com.huawei.it.csrc.application.dto.CsFavoritesMenuInputDTO;
import com.huawei.it.csrc.application.dto.CsFavoritesMenuOutputDTO;
import com.huawei.it.csrc.application.dto.CsFavoritesMenuSaveDTO;

import java.util.List;

/**
 * 菜单收藏服务层
 *
 * <AUTHOR>
 * @since 2022-12-16 06:30:20
 */
public interface ICsFavoritesMenuAppService {
    /**
     * 菜单收藏列表查询
     *
     * @param csFavoritesMenuInputDto csFavoritesMenuInputDto
     * @return List<CsFavoritesMenuOutputDTO>
     */
    List<CsFavoritesMenuOutputDTO> findByField(CsFavoritesMenuInputDTO csFavoritesMenuInputDto);

    /**
     * 新增菜单收藏
     *
     * @param csFavoritesMenuSaveDto csFavoritesMenuSaveDto
     * @return void
     */
    void save(CsFavoritesMenuSaveDTO csFavoritesMenuSaveDto);
}
