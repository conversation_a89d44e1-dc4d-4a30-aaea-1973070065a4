/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.application.service;

import com.huawei.it.csrc.application.dto.CsRecentlyUsedInputDTO;
import com.huawei.it.csrc.application.dto.CsRecentlyUsedOutputDTO;
import com.huawei.it.csrc.application.dto.CsRecentlyUsedSaveDTO;

import java.util.List;

/**
 * 最近使用功能服务层
 *
 * <AUTHOR>
 * @since 2022-12-13 10:19:04
 */
public interface ICsRecentlyUsedAppService {
    /**
     * 最近使用功能列表查询
     *
     * @param csRecentlyUsedInputDto csRecentlyUsedInputDto
     * @return List<CsRecentlyUsedOutputDTO>
     */
    List<CsRecentlyUsedOutputDTO> findByField(CsRecentlyUsedInputDTO csRecentlyUsedInputDto);

    /**
     * 新增最近使用功能
     *
     * @param csRecentlyUsedSaveDto csRecentlyUsedSaveDto
     * @return void
     */
    void save(CsRecentlyUsedSaveDTO csRecentlyUsedSaveDto);

    /**
     * 获取用户数据集
     *
     * @return string
     */
    List<String> getRoleDataAccess();
}
