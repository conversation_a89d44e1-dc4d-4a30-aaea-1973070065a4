/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.application.service;

import com.huawei.it.csrc.base.vo.MessageResultVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;

/**
 * 功能描述：进行中任务权限校验
 *
 * @since 2022-08-24
 */
public interface IFunctionSetAppService {
    MessageResultVO<Integer> getFunctionCount(long userId, String functionName) throws ApplicationException;
}
