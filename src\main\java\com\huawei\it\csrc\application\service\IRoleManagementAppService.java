/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.application.service;

import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.security.RoleVO;

import java.io.IOException;

/**
 * 查找或者创建角色
 *
 * <AUTHOR>
 * @since 2024-04-24 10:01
 */
public interface IRoleManagementAppService {
    RoleVO findOrCreateRole(String roleName, String scope) throws ApplicationException, IOException;
}
