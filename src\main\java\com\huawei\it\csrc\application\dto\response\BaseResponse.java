/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.application.dto.response;

import lombok.Data;
import org.springframework.http.HttpStatus;

/**
 * 统一相应体
 *
 * <AUTHOR>
 * @since 2024-04-25 10:13
 */
@Data
public class BaseResponse<T> {

    /**
     * 业务编码
     */
    private String code;

    /**
     * 业务信息
     */
    private String message;

    /**
     * 业务数据
     */
    private T data;

    public static <T> BaseResponse<T> success() {
        BaseResponse<T> response = new BaseResponse<>();
        response.setCode(String.valueOf(HttpStatus.OK.value()));
        response.setMessage(HttpStatus.OK.getReasonPhrase());
        return response;
    }


    public static <T> BaseResponse<T> success(T data) {
        BaseResponse<T> response = new BaseResponse<>();
        response.setCode(String.valueOf(HttpStatus.OK.value()));
        response.setMessage(HttpStatus.OK.getReasonPhrase());
        response.setData(data);
        return response;
    }

    public static <T> BaseResponse<T> filed() {
        BaseResponse<T> response = new BaseResponse<>();
        response.setCode(String.valueOf(HttpStatus.SERVICE_UNAVAILABLE.value()));
        response.setMessage(HttpStatus.SERVICE_UNAVAILABLE.getReasonPhrase());
        return response;
    }
}
