<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.csrc.infrastructure.dao.ICsRecentlyUsedDao">

    <resultMap type="com.huawei.it.csrc.infrastructure.po.CsRecentlyUsedPO" id="resultMap">
            <result property="id" column="id"/>
            <result property="recentlyUsedId" column="recently_used_id"/>
            <result property="createdBy" column="created_by"/>
            <result property="siteNodeId" column="siteNodeId"/>
            <result property="applicationId" column="application_id"/>
            <result property="creationDate" column="creation_date"/>
            <result property="lastUpdateDate" column="last_update_date"/>
            <result property="lastUpdatedBy" column="last_updated_by"/>
            <result property="deleteFlag" column="delete_flag"/>
            <result property="tenantId" column="tenant_id"/>
            <result property="url" column="url"/>
            <result property="name" column="name"/>
    </resultMap>

    <sql id="tableName">
        cs_recently_used_t
    </sql>

    <sql id="allFieldsWithoutId">
            recently_used_id,
            created_by,
            site_node_id,
            application_id,
            creation_date,
            last_update_date,
            delete_flag,
            tenant_id
    </sql>
    <sql id="allValuesWithoutId">
            #{recentlyUsedId,jdbcType=BIGINT},
            #{createdBy,jdbcType=BIGINT},
            #{siteNodeId,jdbcType=BIGINT},
            #{applicationId,jdbcType=BIGINT},
            now(),
            now(),
            #{deleteFlag,jdbcType=VARCHAR},
            #{tenantId,jdbcType=VARCHAR}
    </sql>

    <sql id="uniqueKeyField">
        id=#{id,jdbcType=BIGINT}
    </sql>

    <sql id="searchFields">
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='createBy != null'>
                AND create_by=#{createBy,jdbcType=BIGINT}
            </if>
            <if test='applicationId != null'>
                AND application_id=#{applicationId,jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <select id="findByField" parameterType="com.huawei.it.csrc.domain.entity.CsRecentlyUsedEntity" resultMap="resultMap">
        SELECT
            cata.id AS siteNodeId,
            cata.url AS url,
            cata."name" AS NAME
        FROM
        cs_recently_used_t us,tpl_catalog_t cata
        WHERE us.site_node_id=cata.id
        AND EXISTS (
        SELECT
        1
        FROM
        tpl_user_role_program_t rol,
        tpl_tenant_role_acl_t acl,
        tpl_function_catalog_t fuc
        WHERE
        rol.role_id = acl.role_id
        AND acl.function_id = fuc.function_id
        AND rol.user_id = us.created_by
        AND fuc.catalog_id = cata.id
        AND rol.org_code = #{recentlyUsedEntity.applicationIdStr,jdbcType=VARCHAR}
        AND rol.user_id = #{recentlyUsedEntity.createdBy,jdbcType=BIGINT}
        )
        and us.application_id = #{recentlyUsedEntity.applicationId,jdbcType=INTEGER}
        AND us.created_by = #{recentlyUsedEntity.createdBy,jdbcType=BIGINT}
        order by us.last_update_date desc LIMIT #{recentlyUsedEntity.showRow,jdbcType=INTEGER}
    </select>

    <insert id="dealToggleMenus" parameterType="com.huawei.it.csrc.infrastructure.po.CsRecentlyUsedPO">
        INSERT INTO <include refid="tableName"/>
        (<include refid="allFieldsWithoutId"/>)
        VALUES
        (<include refid="allValuesWithoutId"/>)
        ON CONFLICT (created_by,site_node_id,application_id)
        DO UPDATE SET last_update_date=NOW()
    </insert>

    <select id="getRoleDataAccess" resultType="string">
        SELECT
        role_id :: TEXT || '#' || program_name COLNAME
        FROM
        tpl_role_t t1
        JOIN (
        SELECT
        role_code,
        string_agg ( program_name, '#' ) program_name
        FROM
        ( SELECT role_code, program_name FROM tpl_role_program_rel_t WHERE del_flag = 'N' ) t1
        GROUP BY
        role_code
        ) t2 ON t1.role_code = t2.role_code
    </select>

</mapper>
