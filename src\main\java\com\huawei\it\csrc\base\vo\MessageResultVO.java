/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
 */

package com.huawei.it.csrc.base.vo;

import lombok.Data;

/**
 * 返回结果VO
 *
 * <AUTHOR>
 * @since 2022.06.01
 */
@Data
public class MessageResultVO<T> {
    private String code = "200";

    private String msg = "ok";

    private T data;

    public MessageResultVO() {
    }

    public MessageResultVO(String code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    /**
     * [简要描述]:成功有参数<br/>
     * [详细描述]:<br/>
     *
     * @param data : 接口响应数据集
     * @return <T> <T>
     **/
    public static <T> MessageResultVO<T> success(T data) {
        MessageResultVO<T> responseVo = new MessageResultVO<>();
        responseVo.setCode("200");
        responseVo.setMsg("ok");
        responseVo.setData(data);
        return responseVo;
    }

    /**
     * [简要描述]:成功<br/>
     * [详细描述]:<br/>
     *
     * @return <T> <T>
     **/
    public static <T> MessageResultVO<T> success() {
        MessageResultVO<T> responseVo = new MessageResultVO<>();
        responseVo.setCode("200");
        responseVo.setMsg("ok");
        return responseVo;
    }

}
