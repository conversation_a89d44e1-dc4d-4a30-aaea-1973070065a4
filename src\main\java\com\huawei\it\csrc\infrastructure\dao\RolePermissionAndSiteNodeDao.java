/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.dao;

import com.huawei.it.csrc.infrastructure.po.AclentryPO;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024-04-24 11:32
 */
public interface RolePermissionAndSiteNodeDao {


    List<AclentryPO> findPermissionAndSiteNodeIdByRoleId(Integer RoleId);


    Integer insertPermissionAndSiteNodeId(List<AclentryPO> aclentryPOList);
}
