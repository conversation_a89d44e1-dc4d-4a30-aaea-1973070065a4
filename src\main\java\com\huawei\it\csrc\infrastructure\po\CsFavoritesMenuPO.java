/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.po;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * The PO of CsFavoritesMenu
 *
 * <AUTHOR>
 * @since 2022-12-16 06:30:20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CsFavoritesMenuPO extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 收藏ID
     */
    private Long favoritesMenuId;

    /**
     * 菜单ID
     */
    private Long siteNodeId;

    /**
     * 收藏标识1为收藏，-1为未收藏
     */
    private int tag;

    /**
     * 最后更新人
     */
    private Long lastUpdateBy;

    /**
     * 删除标识:删除标识:用于表示数据是否软删除，删除后一般业务用户不可见；Y-已删除，N-未删除
     */
    private String deleteFlag;

    /**
     * 作业岛ID:作业岛ID，作业岛的唯一身份标识
     */
    private Long applicationId;

    /**
     * name
     */
    private String name;

    /**
     * url
     */
    private String url;

    /**
     * children
     */
    private String children;

    private String applicationIdStr;
}
