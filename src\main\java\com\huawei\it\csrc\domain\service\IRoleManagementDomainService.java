/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.domain.service;

import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.security.RoleVO;

import java.io.IOException;

/**
 * 查找或复制角色，并复制权限点
 *
 * <AUTHOR>
 * @since 2024-04-24 09:59
 */
public interface IRoleManagementDomainService {
    RoleVO findOrCreateRole(RoleVO queryRole) throws ApplicationException, IOException;

}
