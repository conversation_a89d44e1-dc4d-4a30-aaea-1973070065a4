/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.application.service.impl;

import com.huawei.it.csrc.application.assembler.CsRecentlyUsedConvertDtoAndEntity;
import com.huawei.it.csrc.application.dto.CsRecentlyUsedInputDTO;
import com.huawei.it.csrc.application.dto.CsRecentlyUsedOutputDTO;
import com.huawei.it.csrc.application.dto.CsRecentlyUsedSaveDTO;
import com.huawei.it.csrc.application.service.ICsRecentlyUsedAppService;
import com.huawei.it.csrc.domain.entity.CsRecentlyUsedEntity;
import com.huawei.it.csrc.domain.service.ICsRecentlyUsedDomainService;
import com.huawei.it.jalor5.core.annotation.JalorResource;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.inject.Inject;
import javax.inject.Named;

/**
 * 最近使用功能服务层实现
 *
 * <AUTHOR>
 * @since 2022-12-13 10:19:04
 */
@JalorResource(code = "CsRecentlyUsedAppServiceImpl", desc = "CsRecentlyUsedAppServiceImpl")
@Named
public class CsRecentlyUsedAppServiceImpl implements ICsRecentlyUsedAppService {
    @Inject
    private ICsRecentlyUsedDomainService csRecentlyUsedDomainService;
    @Inject
    private CsRecentlyUsedConvertDtoAndEntity csRecentlyUsedConvert;

    /**
     * 最近使用功能列表查询
     *
     * @param csRecentlyUsedInputDto csRecentlyUsedInputDto
     * @return List<CsRecentlyUsedOutputDTO>
     */
    @Override
    public List<CsRecentlyUsedOutputDTO> findByField(CsRecentlyUsedInputDTO csRecentlyUsedInputDto) {
        CsRecentlyUsedEntity inputEntity = csRecentlyUsedConvert.convertToEntity(csRecentlyUsedInputDto);
        List<CsRecentlyUsedEntity> listEntity = csRecentlyUsedDomainService.findByField(inputEntity);
        List<CsRecentlyUsedOutputDTO> result = new ArrayList<>();
        if (listEntity != null) {
            result = listEntity.stream().map((entity) -> {
                return csRecentlyUsedConvert.convertToDTO(entity);
            }).collect(Collectors.toList());
        }

        return result;
    }

    /**
     * 新增最近使用功能
     *
     * @param csRecentlyUsedSaveDto csRecentlyUsedSaveDto
     * @return void
     */
    @Override
    public void save(CsRecentlyUsedSaveDTO csRecentlyUsedSaveDto) {
        CsRecentlyUsedEntity inputEntity = csRecentlyUsedConvert.convertToEntity(csRecentlyUsedSaveDto);
        csRecentlyUsedDomainService.save(inputEntity);
    }

    @Override
    public List<String> getRoleDataAccess() {
        return csRecentlyUsedDomainService.getRoleDataAccess();
    }
}
