/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.application.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.io.Serializable;
import lombok.Data;

/**
 * The DTO of CsRecentlyUsed For Query's output
 *
 * <AUTHOR>
 * @since 2022-12-13 10:19:04
 */
@Data
public class CsRecentlyUsedOutputDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 菜单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long siteNodeId;

    /**
     * name
     */
    private String name;

    /**
     * url
     */
    private String url;

    /**
     * children
     */
    private String children;
}
