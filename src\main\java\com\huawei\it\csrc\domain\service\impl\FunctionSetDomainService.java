/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
 */

package com.huawei.it.csrc.domain.service.impl;

import com.huawei.it.csrc.domain.repository.IFunctionSetRepo;
import com.huawei.it.csrc.domain.service.IFunctionSetDomainService;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import javax.annotation.Resource;
import javax.inject.Named;

/**
 * 功能描述：功能集查询domain层
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Named
public class FunctionSetDomainService implements IFunctionSetDomainService {
    @Resource
    private IFunctionSetRepo functionSetRepo;

    @Override
    public int getFunctionCount(long userId, String functionName) throws ApplicationException {
        return functionSetRepo.getFunctionCount(userId, functionName);
    }
}
