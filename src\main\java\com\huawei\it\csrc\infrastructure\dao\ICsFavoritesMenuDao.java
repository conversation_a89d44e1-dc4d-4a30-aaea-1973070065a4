/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.dao;

import com.huawei.it.csrc.infrastructure.po.CsFavoritesMenuPO;

import java.util.List;

import org.apache.ibatis.annotations.Param;

/**
 * 菜单收藏仓库接口
 *
 * <AUTHOR>
 * @since 2022-12-16 06:30:20
 */
public interface ICsFavoritesMenuDao {
    void dealFavoritesMenu(CsFavoritesMenuPO po);

    List<CsFavoritesMenuPO> findByField(@Param("favoritesMenuPO") CsFavoritesMenuPO favoritesMenuPO);
}
