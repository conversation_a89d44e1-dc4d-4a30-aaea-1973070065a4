/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.application.service.impl;
import com.huawei.it.csrc.application.service.IProgramManagementAppService;
import com.huawei.it.csrc.domain.service.IProgramManagementDomainService;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.security.ProgramVO;


import javax.inject.Inject;
import javax.inject.Named;
import java.io.IOException;


/**
 *  查找或创建数据范围
 *
 * <AUTHOR>
 * @since 2024-06-27 14:43
 */
@Named
public class ProgramManagementAppService implements IProgramManagementAppService {

    @Inject
    private IProgramManagementDomainService iProgramManagementDomainService;

    @Override
    public ProgramVO findOrCreateProgram(String programName, String scope) throws ApplicationException, IOException {
        ProgramVO queryProgram = new ProgramVO();
        queryProgram.setScope(scope);
        queryProgram.setName(programName);
        queryProgram.setTenantId("-1");
        queryProgram.setProgramId(0);
        return iProgramManagementDomainService.findOrCreateProgram(queryProgram);
    }

}
