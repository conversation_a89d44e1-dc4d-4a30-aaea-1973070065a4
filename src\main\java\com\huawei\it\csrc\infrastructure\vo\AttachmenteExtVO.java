/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.vo;

import com.huawei.it.jalor5.core.base.BaseResourceVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * The VO for result of Search import task and export task
 *
 * <AUTHOR>
 * @since 2022-11-23 05:55:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AttachmenteExtVO extends BaseResourceVO {
    private static final long serialVersionUID = -3120066783822100365L;

    private int taskId;

    private String fileName;

    private String moduleName;

    private Double fileSize;

    // 总条数
    private Integer totalRecords;

    // 成功条数
    private Integer importedRecords;

    private String importAndExportStatus;

    private Date runStartTime;

    private Date runEndTime;

    private String errorMsg;
    private String importExportFlag; // 1表示导入，2表示导出

    private String activity; // 活动

    private String taskType; // 默认为框架导出，若为其他类型，则下载使用downloadUrl

    private long timeTaken; // 耗时

    private String sourceUrl;

    private String defSourceUrl; // 第三方源文件下载路径

    private String defLogUrl; // 第三方源文件下载路径

    private String fileShowType; // 附件展示类型：oneSource：展示一个源文件，two：展示源文件和日志，oneLog：展示一个日志
}
