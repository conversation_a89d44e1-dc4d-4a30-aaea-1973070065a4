/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.application.assembler;

import com.huawei.it.csrc.application.dto.CsRecentlyUsedInputDTO;
import com.huawei.it.csrc.application.dto.CsRecentlyUsedOutputDTO;
import com.huawei.it.csrc.application.dto.CsRecentlyUsedSaveDTO;
import com.huawei.it.csrc.domain.entity.CsRecentlyUsedEntity;

import javax.inject.Named;

/**
 * The Convert Utils for CsRecentlyUsed
 *
 * <AUTHOR>
 * @since 2022-12-13 10:19:04
 */
@Named
public class CsRecentlyUsedConvertDtoAndEntity {
    /**
     * 将CsRecentlyUsedDTO转换为CsRecentlyUsedEntity
     *
     * @param csRecentlyUsedInputDto csRecentlyUsedInputDto
     * @return CsRecentlyUsedEntity
     */
    public CsRecentlyUsedEntity convertToEntity(CsRecentlyUsedInputDTO csRecentlyUsedInputDto) {
        CsRecentlyUsedEntity csRecentlyUsedEntity = new CsRecentlyUsedEntity();
        if (csRecentlyUsedInputDto == null) {
            return csRecentlyUsedEntity;
        }
        csRecentlyUsedEntity.setApplicationId(csRecentlyUsedInputDto.getApplicationId());
        csRecentlyUsedEntity.setTenantId(csRecentlyUsedInputDto.getTenantId());
        return csRecentlyUsedEntity;
    }

    /**
     * 将CsRecentlyUsedSaveDTO转换为CsRecentlyUsedEntity
     *
     * @param csRecentlyUsedSaveDto csRecentlyUsedSaveDto
     * @return CsRecentlyUsedEntity
     */
    public CsRecentlyUsedEntity convertToEntity(CsRecentlyUsedSaveDTO csRecentlyUsedSaveDto) {
        CsRecentlyUsedEntity csRecentlyUsedEntity = new CsRecentlyUsedEntity();
        if (csRecentlyUsedSaveDto == null) {
            return csRecentlyUsedEntity;
        }
        csRecentlyUsedEntity.setCreatedBy(csRecentlyUsedSaveDto.getCreatedBy());
        csRecentlyUsedEntity.setSiteNodeId(csRecentlyUsedSaveDto.getSiteNodeId());
        csRecentlyUsedEntity.setApplicationId(csRecentlyUsedSaveDto.getApplicationId());
        csRecentlyUsedEntity.setTenantId(csRecentlyUsedSaveDto.getTenantId());
        return csRecentlyUsedEntity;
    }

    /**
     * 将CsRecentlyUsedEntity转换为CsRecentlyUsedOutputDTO
     *
     * @param csRecentlyUsedEntity csRecentlyUsedEntity
     * @return CsRecentlyUsedOutputDTO
     */
    public CsRecentlyUsedOutputDTO convertToDTO(CsRecentlyUsedEntity csRecentlyUsedEntity) {
        CsRecentlyUsedOutputDTO csRecentlyUsedOutputDto = new CsRecentlyUsedOutputDTO();
        if (csRecentlyUsedEntity == null) {
            return csRecentlyUsedOutputDto;
        }
        csRecentlyUsedOutputDto.setSiteNodeId(csRecentlyUsedEntity.getSiteNodeId());
        csRecentlyUsedOutputDto.setName(csRecentlyUsedEntity.getName());
        csRecentlyUsedOutputDto.setUrl(csRecentlyUsedEntity.getUrl());
        return csRecentlyUsedOutputDto;
    }
}
