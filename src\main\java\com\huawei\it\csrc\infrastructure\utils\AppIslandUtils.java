/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;

public class AppIslandUtils {
    private static final Logger log = LogManager.getLogger(AppIslandUtils.class);

    public static String getAppIslandCacheKey() {
        String key = String.format("efm_appisland_application_%s", RequestContextUtils.getCurUserId());
        log.info("getAppIslandCacheKey key = {}", key);
        return key;
    }

    public static RMapCache<String, String> getRequestContext() {
        return SpringContextUtil.getBean(RedissonClient.class).getMapCache(getAppIslandCacheKey());
    }

    public static Long getApplicationId() {
        String applicationId = getRequestContext().get("applicationId");
        log.info("getApplicationId value = {}", applicationId);
        return StringUtils.isBlank(applicationId) ? 123523L : Long.valueOf(applicationId);
    }
}
