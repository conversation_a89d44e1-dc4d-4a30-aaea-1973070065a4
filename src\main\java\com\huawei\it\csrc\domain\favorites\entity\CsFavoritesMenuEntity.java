/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.domain.favorites.entity;

import java.io.Serializable;
import lombok.Data;

/**
 * The Entity of CsFavoritesMenu
 *
 * <AUTHOR>
 * @since 2022-12-16 06:30:20
 */
@Data
public class CsFavoritesMenuEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 创建人
     */
    private Long createdBy;

    private String applicationIdStr;

    public String getApplicationIdStr() {
        if (this.applicationId != null) {
            applicationIdStr = String.valueOf(this.applicationId);
        }
        return applicationIdStr;
    }

    /**
     * 菜单ID
     */
    private Long siteNodeId;

    /**
     * url
     */
    private String url;

    /**
     * 作业岛ID:作业岛ID，作业岛的唯一身份标识
     */
    private Long applicationId;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * name
     */
    private String name;

    /**
     * children
     */
    private String children;
}
