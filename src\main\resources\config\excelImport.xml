<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<excel xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.huawei.com/xml/ns/jalor/excel"
	   id="jalor5.security.user"  privilege="Service$User$import">
	  <validators id = "userGroupvalidator">
		<validator
		   		name="roleExist"
		   		class="com.huawei.it.jalor5.security.importer.validate.impl.RoleExistValidator"></validator>
			<validator
		   		name="programExist"
		   		class="com.huawei.it.jalor5.security.importer.validate.impl.ProgramExistValidator"></validator>
		   	<validator
		   		name="localUserExist"
		   		class="com.huawei.it.jalor5.security.importer.validate.impl.LocalUserExistValidator"></validator>
		   	<validator
		   		name="VirtualUser"
		   		class="com.huawei.it.jalor5.security.importer.validate.impl.VirtualUserValidator"></validator>
		   	<validator
		   		name="VirtualUserCN"
		   		class="com.huawei.it.jalor5.security.importer.validate.impl.VirtualUserCNValidator"></validator>
			<validator
	   		name="compareUserDate"
	   		class="com.huawei.it.jalor5.security.importer.validate.impl.UserCompareDateValidator"></validator>
	</validators>
	<!-- User Basic Information Export -->
	<sheet name="userBasicInformation" titleRowIndex="1" dataRowIndex="2"
		voClassName="com.huawei.it.jalor5.security.UserVO"
		consumerBean="IExcelDataConsumer.userInformationImportConsumer">
		<!-- 用户 Account-->
		<column name="User Account" type="String" fieldName="userAccount" annotation="用户 Account (必填)(只能输入字符 a-zA-Z0-9_-)">
			<validator type="required"/>
			<validator type="stringLength">
				<param name="maxLength">50</param>
			</validator>
			<validator type="pattern">
				<param name="pattern">^[a-zA-Z0-9_.]+</param>
				<message key="jalor.excel.validate.spec2"></message>
			</validator>
            <validator type="VirtualUser">
            	<param name="notInLength">8,9,11</param>
            </validator>
		</column>
		 <!-- 工号 -->
		<column name="Employee ID" type="String" fieldName="employeeNumber" annotation="用户工号 ">
			<validator type="stringLength">
				<param name="maxLength">50</param>
			</validator>
		</column>
		<!-- userCN -->
		<column name="Common Name" type="String" fieldName="userCN" annotation="userCN (虚拟用户必填,并且只能输入字符 a-zA-Z0-9_-)">
			<validator type="stringLength">
				<param name="maxLength">200</param>
			</validator>
			<validator type="VirtualUserCN"/>
		</column>
		<!-- 用户 Email -->
		<!--<column name="User Email" type="String" fieldName="email" annotation="用户 Email">
			<validator type="stringLength">
				<param name="maxLength">50</param>
			</validator>
			<validator type="email"/>
		</column>-->
		<!-- 用户 type -->
		<column name="User Type" type="String" fieldName="userType" annotation="userType(虚拟用户必填Virtual)">
			<validator type="stringLength">
				<param name="maxLength">50</param>
			</validator>
		</column>
	</sheet>
	<!-- User Permission Export -->
	<sheet name="userPermission" titleRowIndex="1" dataRowIndex="2"
		voClassName="com.huawei.it.jalor5.security.UserSinglePermissionVO"
		consumerBean="IExcelDataConsumer.userPermissionImportConsumer">
		<!-- 用户 工号-->
		<!--<column name="Employee ID" type="String" fieldName="employeeNumber" annotation="用户 工号(必填)">
			<validator type="required"/>
			<validator type="pattern">
				<param name="pattern">^[a-zA-Z0-9_-]+</param>
				<message key="jalor.excel.validate.spec2"></message>
			</validator>
			<validator type="localUserExist"/>
		</column>-->
		<!-- 用户 Account-->
		<column name="User Account" type="String" fieldName="userAccount" annotation="用户 Account (必填)(只能输入字符 a-zA-Z0-9_-)">
			<validator type="required"/>
			<validator type="stringLength">
				<param name="maxLength">50</param>
			</validator>
			<validator type="pattern">
				<param name="pattern">^[a-zA-Z0-9_.]+</param>
				<message key="jalor.excel.validate.spec2"></message>
			</validator>
            <validator type="localUserExist"/>
		</column>
		 <!-- 角色名 -->
		<column name="Role Name" type="String" fieldName="roleName" annotation="角色名 (必填)">
			<validator type="required"/>
			<validator type="roleExist"/>
		</column>
		<!-- ProgramVO.name -->
		<column name="Program" type="String" fieldName="programName" annotation="programName (必填)">
			<validator type="required"/>
			<validator type="programExist"/>
		</column>
		<!-- 用户 有效起始日期 -->
		<column name="Start Date" type="String" fieldName="startDate" annotation="有效起始日期yyyy-MM-dd (必填)">
			<validator type="required"/>
			<validator type="date">
				<param name="format">yyyy-MM-dd</param>
			</validator>
			<validator type="dateRange">
				<param name="format">yyyy-MM-dd</param>
				<param name="maxTitle">End Date</param>
			</validator>
		</column>
		<!-- 用户  有效结束日期 -->
		<column name="End Date" type="String" fieldName="endDate" annotation="有效结束日期yyyy-MM-dd (必填)">
			<validator type="required"/>
			<validator type="date">
				<param name="format">yyyy-MM-dd</param>
			</validator>
			<validator type="dateRange">
				<param name="format">yyyy-MM-dd</param>
				<param name="minTitle">Start Date</param>
				<param name="minDate">Sysdate</param>
			</validator>
			<validator type="compareUserDate">
				<param name="format">yyyy-MM-dd</param>
				<param name="compareTitle">Start Date</param>
			</validator>
		</column>
		<!-- 权限共享组 -->
		<column name="scope" type="String" fieldName="scope" annotation="权限共享组">
			<validator type="required"/>
		</column>
	</sheet>
</excel>