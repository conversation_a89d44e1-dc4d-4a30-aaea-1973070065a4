/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.repository.impl;

import com.huawei.it.csrc.domain.favorites.entity.CsFavoritesMenuEntity;
import com.huawei.it.csrc.domain.favorites.repository.ICsFavoritesMenuRepo;
import com.huawei.it.csrc.infrastructure.convert.CsFavoritesMenuConvertEntityAndPo;
import com.huawei.it.csrc.infrastructure.dao.ICsFavoritesMenuDao;
import com.huawei.it.csrc.infrastructure.po.CsFavoritesMenuPO;
import com.huawei.it.csrc.infrastructure.utils.RequestContextUtils;
import com.huawei.it.csrc.infrastructure.utils.SnowflakeIdWorkerUtil;
import com.huawei.it.csrc.infrastructure.utils.TranslationUtils;
import com.huawei.it.jalor5.core.request.impl.RequestContext;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.inject.Inject;
import javax.inject.Named;

/**
 * 菜单收藏仓库接口实现
 *
 * <AUTHOR>
 * @since 2022-12-16 06:30:20
 */
@Named
public class CsFavoritesMenuRepoImpl implements ICsFavoritesMenuRepo {
    @Inject
    private ICsFavoritesMenuDao csFavoritesMenuDao;

    @Inject
    private CsFavoritesMenuConvertEntityAndPo csFavoritesMenuConvert;

    /**
     * 菜单收藏列表查询
     *
     * @param csFavoritesMenuEntity csFavoritesMenuEntity
     * @return List<CsFavoritesMenuEntity>
     */
    @Override
    public List<CsFavoritesMenuEntity> findByField(CsFavoritesMenuEntity csFavoritesMenuEntity) {
        List<CsFavoritesMenuEntity> result = new ArrayList<>();
        Long createdBy = RequestContextUtils.getCurUser().getUserId();
        String language = RequestContext.getCurrent().getUserLanguage();
        csFavoritesMenuEntity.setCreatedBy(createdBy);
        CsFavoritesMenuPO queryPO = csFavoritesMenuConvert.convertToPO(csFavoritesMenuEntity);
        List<CsFavoritesMenuPO> listCsFavoritesMenuPo = csFavoritesMenuDao.findByField(queryPO);
        if (listCsFavoritesMenuPo != null) {
            result = listCsFavoritesMenuPo.stream().map((po) -> {
                // name翻译
                po.setName(TranslationUtils.translationName(language, po.getName()));
                return csFavoritesMenuConvert.convertToEntity(po);
            }).collect(Collectors.toList());
        }
        return result;
    }

    /**
     * 菜单收藏新增
     *
     * @param csFavoritesMenuEntity csFavoritesMenuEntity
     * @return void
     */
    @Override
    public void save(CsFavoritesMenuEntity csFavoritesMenuEntity) {
        CsFavoritesMenuPO savePO = csFavoritesMenuConvert.convertToPO(csFavoritesMenuEntity);
        Long createdBy = RequestContextUtils.getCurUser().getUserId();
        savePO.setCreatedBy(createdBy);
        savePO.setId(SnowflakeIdWorkerUtil.generateId());
        savePO.setFavoritesMenuId(SnowflakeIdWorkerUtil.generateId());
        csFavoritesMenuDao.dealFavoritesMenu(savePO);
    }
}
