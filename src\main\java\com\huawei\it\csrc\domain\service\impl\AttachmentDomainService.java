/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
 */

package com.huawei.it.csrc.domain.service.impl;

import com.huawei.it.csrc.application.dto.ImportTaskDTO;
import com.huawei.it.csrc.domain.entity.ImportTaskEntity;
import com.huawei.it.csrc.domain.repository.IAttachmentRepo;
import com.huawei.it.csrc.domain.service.IAttachmentDomainService;
import com.huawei.it.csrc.infrastructure.utils.S3ClientUtil;
import com.huawei.it.csrc.infrastructure.vo.AttachmenteExtVO;
import com.huawei.it.csrc.infrastructure.vo.ImportAndExportVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import lombok.extern.log4j.Log4j2;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;

/**
 * 功能描述：附件查询服务domain层
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@Named
@Log4j2
public class AttachmentDomainService implements IAttachmentDomainService {
    @Resource
    private IAttachmentRepo attachmentRepo;

    @Value("${jalor.store.s3.bucketname:csrc-dev01}")
    private String bucketName;

    @Inject
    private S3ClientUtil s3ClientUtil;

    @Override
    public Map<Long, String> findRqDocByIds(List<Long> taskIds) throws ApplicationException {
        return attachmentRepo.findRqDocByIds(taskIds);
    }

    @Override
    public ImportTaskDTO findRqDocByImportId(Long taskIds) throws ApplicationException {
        ImportTaskDTO importTaskDTO = new ImportTaskDTO();
        ImportTaskEntity importTaskEntity = attachmentRepo.findRqDocByImportId(taskIds);
        if (importTaskEntity != null) {
            BeanUtils.copyProperties(importTaskEntity, importTaskDTO);
        }
        return importTaskDTO;
    }

    @Override
    public ImportTaskDTO findRqDocByExportId(Long taskIds) throws ApplicationException {
        ImportTaskDTO importTaskDTO = new ImportTaskDTO();
        ImportTaskEntity importTaskEntity = attachmentRepo.findRqDocByExportId(taskIds);
        if (importTaskEntity != null) {
            BeanUtils.copyProperties(importTaskEntity, importTaskDTO);
        }
        return importTaskDTO;
    }

    @Override
    public PagedResult<AttachmenteExtVO> findImportTaskList(ImportAndExportVO var1, PageVO var2)
            throws ApplicationException {
        return attachmentRepo.findImportTaskList(var1, var2);
    }

    @Override
    public PagedResult<AttachmenteExtVO> findExportTaskList(ImportAndExportVO var1, PageVO var2)
            throws ApplicationException {
        return attachmentRepo.findExportTaskList(var1, var2);
    }

    /**
     * 文件下载
     *
     * @param id id
     * @param fileName fileName
     * @param response response
     */
    @Override
    public void download(String id, String fileName, HttpServletResponse response) {
        log.info("AttachmentService downloadAttachment begin id:{}, fileName:{}", id, fileName);
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        response.setCharacterEncoding(StandardCharsets.UTF_8.toString());
        log.info("AttachmentService downloadAttachment fileName:{} bucketName:{} key:{}", fileName, bucketName, id);
        String realFileName = null;
        try {
            realFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()).replace("+", "%20");
        } catch (UnsupportedEncodingException ex) {
            log.error("Get file name failed.", ex.getMessage());
        }
        response.setHeader("Content-Disposition", "attachment;filename=" + realFileName);
        try {
            s3ClientUtil.init();
            s3ClientUtil.downloadStream(bucketName, id, response.getOutputStream());
        } catch (IOException e) {
            log.error("down file occour io exception");
        }
    }
}
