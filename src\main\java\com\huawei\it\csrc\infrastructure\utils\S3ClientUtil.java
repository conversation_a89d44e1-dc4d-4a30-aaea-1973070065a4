/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.utils;

import com.amazonaws.services.s3.model.ObjectMetadata;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.OutputStream;

@Slf4j
@Component
public class S3ClientUtil {
    private S3Client s3Client;
    @Value("${s3.csrc.accessKey}")
    private String accessKey;
    @Value("${s3.csrc.secretKey}")
    private String secretKey;
    @Value("${s3.pdmcore.host}")
    private String host;

    public void init() {
        try {
            log.info("S3 init success. host:{}", this.host);
            this.s3Client = new S3Client();
            this.s3Client.init(this.accessKey, this.secretKey, this.host);
        } catch (Exception var2) {
            log.error("S3 init success. host:{}", this.host);
        }
    }

    public ObjectMetadata downloadStream(String bucketName, String key, OutputStream out) {
        return this.s3Client.downloadStream(bucketName, key, out);
    }
}
