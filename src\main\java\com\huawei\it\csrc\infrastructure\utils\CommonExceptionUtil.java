/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.utils;

import com.huawei.it.csrc.infrastructure.exception.CsrcServiceException;
import com.huawei.it.jalor5.core.exception.impl.ExceptionMessageManager;
import com.huawei.it.jalor5.core.ioc.Jalor;

import org.apache.commons.lang3.StringUtils;

import java.text.MessageFormat;
import java.util.ResourceBundle;

public class CommonExceptionUtil {
    private static ResourceBundle resourceBundle;

    public static CsrcServiceException getI18nCommonException(String errorCode, Object... args) {
        String msg = getI18nStr(errorCode);
        if (StringUtils.isNotEmpty(msg)) {
            msg = MessageFormat.format(msg, args);
        } else {
            msg = ExceptionMessageManager.getMessage(errorCode, args);
        }

        return new CsrcServiceException(errorCode, msg);
    }

    private static String getI18nStr(String errorCode) {
        if (resourceBundle == null) {
            synchronized (CommonExceptionUtil.class) {
                if (resourceBundle == null) {
                    resourceBundle = Jalor.getContext().getBean("i18nResourceBundle", ResourceBundle.class);
                }
            }
        }

        return resourceBundle.getString(errorCode);
    }
}
