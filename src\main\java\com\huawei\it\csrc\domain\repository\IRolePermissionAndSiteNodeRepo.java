/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.domain.repository;

import com.huawei.it.csrc.infrastructure.po.AclentryPO;

import java.util.List;

/**
 * 查找操作角色的权限点和栏目信息
 *
 * <AUTHOR>
 * @since 2024-04-24 11:28
 */
public interface IRolePermissionAndSiteNodeRepo {
    List<AclentryPO> findPermissionAndSiteNodeIdByRoleId(Integer RoleId);

    Integer insertPermissionAndSiteNodeId(List<AclentryPO> aclentryPOList);
}
