package com.huawei.it.csrc.application.service;

import com.huawei.it.csrc.application.assembler.CsRecentlyUsedConvertDtoAndEntity;
import com.huawei.it.csrc.application.dto.CsRecentlyUsedInputDTO;
import com.huawei.it.csrc.application.dto.CsRecentlyUsedOutputDTO;
import com.huawei.it.csrc.application.dto.CsRecentlyUsedSaveDTO;
import com.huawei.it.csrc.application.service.impl.CsRecentlyUsedAppServiceImpl;
import com.huawei.it.csrc.domain.entity.CsRecentlyUsedEntity;
import com.huawei.it.csrc.domain.service.ICsRecentlyUsedDomainService;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

public class CsRecentlyUsedAppServiceTest extends BaseTest {

    @InjectMocks
    private CsRecentlyUsedAppServiceImpl csRecentlyUsedAppServiceImpl = new CsRecentlyUsedAppServiceImpl();

    @Mock
    private ICsRecentlyUsedDomainService csRecentlyUsedDomainService;
    @Mock
    private CsRecentlyUsedConvertDtoAndEntity csRecentlyUsedConvert;

    /**
     * Init mock.
     */
    @Before
    public void initMock() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * findPagedList
     *
     * @throws ApplicationException the application exception
     */
    @Test
    public void findByField() throws ApplicationException {
        CsRecentlyUsedInputDTO csRecentlyUsedInputDto = new CsRecentlyUsedInputDTO();

        List<CsRecentlyUsedEntity> listEntity = new ArrayList<>();
        CsRecentlyUsedEntity csRecentlyUsedEntity = new CsRecentlyUsedEntity();
        csRecentlyUsedEntity.setApplicationId(1L);
        listEntity.add(csRecentlyUsedEntity);
        Mockito.when(csRecentlyUsedDomainService.findByField(Mockito.any()))
                .thenReturn(listEntity);
        List<CsRecentlyUsedOutputDTO> result = csRecentlyUsedAppServiceImpl.findByField(csRecentlyUsedInputDto);
        Assert.assertNotNull(result);
    }

    @Test
    public void findByFieldIsNull() throws ApplicationException {
        CsRecentlyUsedInputDTO csRecentlyUsedInputDto = new CsRecentlyUsedInputDTO();
        Mockito.when(csRecentlyUsedDomainService.findByField(Mockito.any()))
                .thenReturn(null);
        List<CsRecentlyUsedOutputDTO> result = csRecentlyUsedAppServiceImpl.findByField(csRecentlyUsedInputDto);
        Assert.assertNotNull(result);
    }

    /**
     * findPagedList
     *
     * @throws ApplicationException the application exception
     */
    @Test
    public void save() throws ApplicationException {
        CsRecentlyUsedSaveDTO csRecentlyUsedSaveDto = new CsRecentlyUsedSaveDTO();
        csRecentlyUsedAppServiceImpl.save(csRecentlyUsedSaveDto);
    }

}

