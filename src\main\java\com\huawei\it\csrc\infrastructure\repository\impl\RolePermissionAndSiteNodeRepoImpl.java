/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.repository.impl;

import com.huawei.it.csrc.domain.repository.IRolePermissionAndSiteNodeRepo;
import com.huawei.it.csrc.infrastructure.dao.RolePermissionAndSiteNodeDao;
import com.huawei.it.csrc.infrastructure.po.AclentryPO;

import javax.annotation.Resource;
import javax.inject.Named;
import java.util.List;

/**
 * 进行权限低的查找
 *
 * <AUTHOR>
 * @since 2024-04-24 11:31
 */
@Named
public class RolePermissionAndSiteNodeRepoImpl implements IRolePermissionAndSiteNodeRepo {

    @Resource
    private RolePermissionAndSiteNodeDao RolePermissionAndSiteNodeDao;

    @Override
    public List<AclentryPO> findPermissionAndSiteNodeIdByRoleId(Integer roleId) {
        return RolePermissionAndSiteNodeDao.findPermissionAndSiteNodeIdByRoleId(roleId);
    }

    @Override
    public Integer insertPermissionAndSiteNodeId(List<AclentryPO> aclentryPOList) {
        return RolePermissionAndSiteNodeDao.insertPermissionAndSiteNodeId(aclentryPOList);
    }

}
