/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.application.service;

import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.security.ProgramVO;

import java.io.IOException;

/**
 * 查找或创建数据范围
 *
 * <AUTHOR>
 * @since 2024-06-27 14:40
 */
public interface IProgramManagementAppService {

    ProgramVO findOrCreateProgram(String programName, String scope) throws ApplicationException, IOException;
}
