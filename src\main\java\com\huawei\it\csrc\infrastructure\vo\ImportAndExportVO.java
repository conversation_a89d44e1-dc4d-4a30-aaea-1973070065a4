/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.vo;

import lombok.Data;

import java.util.List;

/**
 * The VO of Search import task and export task
 *
 * <AUTHOR>
 * @since 2022-11-21 05:55:00
 */
@Data
public class ImportAndExportVO {
    private String importExportFlag; // 1表示导入，2表示导出

    private String fileName; // 文件名

    private String status; // 等待是0，处理中是1，成功是2，部分成功3，失败是4

    private String runStartTime; // 开始时间

    private String runEndTime; // 结束时间

    private List<String> activityList; // 活动列表

    private List<String> appNames; // 基础配置共享组列表

    private long currentUserId; // 当前登录用户的userId

    private String language; // 当前系统语言
}
