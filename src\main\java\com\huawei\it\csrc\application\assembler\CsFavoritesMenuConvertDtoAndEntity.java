/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.application.assembler;

import com.huawei.it.csrc.application.dto.CsFavoritesMenuInputDTO;
import com.huawei.it.csrc.application.dto.CsFavoritesMenuOutputDTO;
import com.huawei.it.csrc.application.dto.CsFavoritesMenuSaveDTO;
import com.huawei.it.csrc.domain.favorites.entity.CsFavoritesMenuEntity;

import javax.inject.Named;

/**
 * The Convert Utils for CsFavoritesMenu
 *
 * <AUTHOR>
 * @since 2022-12-16 06:30:20
 */
@Named
public class CsFavoritesMenuConvertDtoAndEntity {
    /**
     * 将CsFavoritesMenuDTO转换为CsFavoritesMenuEntity
     *
     * @param csFavoritesMenuInputDto csFavoritesMenuInputDto
     * @return CsFavoritesMenuEntity
     */
    public CsFavoritesMenuEntity convertToEntity(CsFavoritesMenuInputDTO csFavoritesMenuInputDto) {
        CsFavoritesMenuEntity csFavoritesMenuEntity = new CsFavoritesMenuEntity();
        if (csFavoritesMenuInputDto == null) {
            return csFavoritesMenuEntity;
        }
        csFavoritesMenuEntity.setCreatedBy(csFavoritesMenuInputDto.getCreatedBy());
        csFavoritesMenuEntity.setApplicationId(csFavoritesMenuInputDto.getApplicationId());
        csFavoritesMenuEntity.setTenantId(csFavoritesMenuInputDto.getTenantId());
        return csFavoritesMenuEntity;
    }

    /**
     * 将CsFavoritesMenuSaveDTO转换为CsFavoritesMenuEntity
     *
     * @param csFavoritesMenuSaveDto csFavoritesMenuSaveDto
     * @return CsFavoritesMenuEntity
     */
    public CsFavoritesMenuEntity convertToEntity(CsFavoritesMenuSaveDTO csFavoritesMenuSaveDto) {
        CsFavoritesMenuEntity csFavoritesMenuEntity = new CsFavoritesMenuEntity();
        if (csFavoritesMenuSaveDto == null) {
            return csFavoritesMenuEntity;
        }

        csFavoritesMenuEntity.setSiteNodeId(csFavoritesMenuSaveDto.getSiteNodeId());
        csFavoritesMenuEntity.setApplicationId(csFavoritesMenuSaveDto.getApplicationId());
        return csFavoritesMenuEntity;
    }

    /**
     * 将CsFavoritesMenuEntity转换为CsFavoritesMenuOutputDTO
     *
     * @param csFavoritesMenuEntity csFavoritesMenuEntity
     * @return CsFavoritesMenuOutputDTO
     */
    public CsFavoritesMenuOutputDTO convertToDTO(CsFavoritesMenuEntity csFavoritesMenuEntity) {
        CsFavoritesMenuOutputDTO csFavoritesMenuOutputDto = new CsFavoritesMenuOutputDTO();
        if (csFavoritesMenuEntity == null) {
            return csFavoritesMenuOutputDto;
        }
        csFavoritesMenuOutputDto.setSiteNodeId(csFavoritesMenuEntity.getSiteNodeId());
        csFavoritesMenuOutputDto.setName(csFavoritesMenuEntity.getName());
        csFavoritesMenuOutputDto.setUrl(csFavoritesMenuEntity.getUrl());
        csFavoritesMenuOutputDto.setChildren(csFavoritesMenuEntity.getChildren());
        return csFavoritesMenuOutputDto;
    }
}
