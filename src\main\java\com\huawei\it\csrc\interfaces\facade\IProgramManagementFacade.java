/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.interfaces.facade;

import com.huawei.it.csrc.application.dto.response.BaseResponse;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.security.ProgramVO;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import java.io.IOException;

/**
 * 查找或创建数据范围
 *
 * <AUTHOR>
 * @since 2024-06-27 16:17
 */
@Path("/v1/programManagement")
@Produces(MediaType.APPLICATION_JSON)
public interface IProgramManagementFacade {

    @GET
    @Path("/findOrCreateProgram")
    BaseResponse<ProgramVO> findOrCreateProgram(@QueryParam("programName")String programName, @QueryParam("scope")String scope) throws ApplicationException, IOException;
}
