/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.utils;

import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;

import java.net.Inet4Address;
import java.net.UnknownHostException;
import java.util.Locale;

public class SnowflakeIdWorkerUtil {
    private long workerId;
    private long dataCenterId;
    private long sequence = 0L;
    private long lastTimestamp = -1L;
    private static final SnowflakeIdWorkerUtil ID_WORKER = new SnowflakeIdWorkerUtil(getWorkId(), getDataCenterId());

    public SnowflakeIdWorkerUtil(long workerId, long dataCenterId) {
        if (workerId <= 31L && workerId >= 0L) {
            if (dataCenterId <= 31L && dataCenterId >= 0L) {
                this.workerId = workerId;
                this.dataCenterId = dataCenterId;
            } else {
                throw new IllegalArgumentException(
                        String.format(Locale.ROOT, "dataCenterId can't be greater than %d or less than 0", 31L));
            }
        } else {
            throw new IllegalArgumentException(
                    String.format(Locale.ROOT, "workerId can't be greater than %d or less than 0", 31L));
        }
    }

    public synchronized long nextId() {
        long timestamp = this.timeGen();
        if (timestamp < lastTimestamp) {
            throw CommonExceptionUtil.getI18nCommonException("huawei.common.0011", lastTimestamp - timestamp);
        } else {
            if (lastTimestamp == timestamp) {
                sequence = sequence + 1L & 4095L;
                if (sequence == 0L) {
                    timestamp = this.tilNextMillis(lastTimestamp);
                }
            } else {
                sequence = 0L;
            }

            lastTimestamp = timestamp;
            return timestamp - 1489111610226L << 22 | this.dataCenterId << 17 | this.workerId << 12 | this.sequence;
        }
    }

    protected long tilNextMillis(long lastTimestamp) {
        long timestamp;
        for (timestamp = this.timeGen(); timestamp <= lastTimestamp; timestamp = this.timeGen()) {
        }

        return timestamp;
    }

    protected long timeGen() {
        return System.currentTimeMillis();
    }

    private static Long getWorkId() {
        try {
            String hostAddress = Inet4Address.getLocalHost().getHostAddress();
            int[] ints = StringUtils.toCodePoints(hostAddress);
            int sums = 0;
            int[] var3 = ints;
            int var4 = ints.length;

            for (int var5 = 0; var5 < var4; ++var5) {
                int bb = var3[var5];
                sums += bb;
            }

            return (long) (sums % 32);
        } catch (UnknownHostException var7) {
            return RandomUtils.nextLong(0L, 31L);
        }
    }

    private static Long getDataCenterId() {
        int[] ints = StringUtils.toCodePoints(SystemUtils.getHostName());
        int sums = 0;
        int[] var2 = ints;
        int var3 = ints.length;

        for (int var4 = 0; var4 < var3; ++var4) {
            int ii = var2[var4];
            sums += ii;
        }

        return (long) (sums % 32);
    }

    public static synchronized Long generateId() {
        return ID_WORKER.nextId();
    }
}
