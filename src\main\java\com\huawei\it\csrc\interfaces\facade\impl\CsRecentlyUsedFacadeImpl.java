/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.interfaces.facade.impl;

import com.huawei.it.csrc.application.dto.CsRecentlyUsedInputDTO;
import com.huawei.it.csrc.application.dto.CsRecentlyUsedOutputDTO;
import com.huawei.it.csrc.application.dto.CsRecentlyUsedSaveDTO;
import com.huawei.it.csrc.application.dto.MessageResultDTO;
import com.huawei.it.csrc.application.service.ICsRecentlyUsedAppService;
import com.huawei.it.csrc.interfaces.facade.ICsRecentlyUsedFacade;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.request.RequestConstants;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

/**
 * 最近使用功能接口
 *
 * <AUTHOR>
 * @since 2022-12-13 10:19:04
 */
@Named
@JalorResource(code = "CsRecentlyUsedFacadeImpl", desc = "Cs Recently Used Facade Impl")
public class CsRecentlyUsedFacadeImpl implements ICsRecentlyUsedFacade {
    @Inject
    private ICsRecentlyUsedAppService csRecentlyUsedService;

    /**
     * 最近使用功能列表查询
     *
     * @param csRecentlyUsedInputDto csRecentlyUsedInputDto
     * @return MessageResultDTO<List < CsRecentlyUsedOutputDTO>>
     */
    @Override
    @Audit
    @JalorOperation(code = RequestConstants.READ, desc = RequestConstants.READ_DESC)
    public MessageResultDTO<List<CsRecentlyUsedOutputDTO>> findByField(CsRecentlyUsedInputDTO csRecentlyUsedInputDto) {
        List<CsRecentlyUsedOutputDTO> resultList = csRecentlyUsedService.findByField(csRecentlyUsedInputDto);
        return MessageResultDTO.success(resultList);
    }

    /**
     * 新增最近使用功能
     *
     * @param csRecentlyUsedSaveDto csRecentlyUsedSaveDto
     * @return MessageResultDTO
     */
    @Override
    @Audit
    @JalorOperation(code = RequestConstants.CREATE, desc = RequestConstants.READ_DESC)
    public MessageResultDTO save(CsRecentlyUsedSaveDTO csRecentlyUsedSaveDto) {
        csRecentlyUsedService.save(csRecentlyUsedSaveDto);
        return MessageResultDTO.success();
    }

    @Override
    @Audit
    @JalorOperation(code = RequestConstants.READ, desc = RequestConstants.READ_DESC)
    public List<String> getRoleDataAccess() {
        return csRecentlyUsedService.getRoleDataAccess();
    }
}
