/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
 */

package com.huawei.it.csrc;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.parser.ParserConfig;

import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 功能描述：公服启动类
 *
 * <AUTHOR>
 * @since 2022-10-20
 */
@SpringBootApplication(scanBasePackages = {"com.huawei"})
public class CommonServiceApplication {

    private static final String SUPPORT_TYPE_PACKAGE = "com.huawei.it";

    public static void main(String[] args) {
        // hwenvironment该配置只在dev，sit，uat上配置值为uat，生产环境需去掉该配置，在容器自定义参数上配置，值为pro
        System.setProperty("hwenvironment", "uat");
        SpringApplication.run(CommonServiceApplication.class, args);
        // fastjson开启safeMode
        ParserConfig.getGlobalInstance().setSafeMode(true);
        ParserConfig.getGlobalInstance().setAutoTypeSupport(true);
        ParserConfig.getGlobalInstance().addAutoTypeCheckHandler(new UserDefineAutoCheckHandler());
    }

    private static class UserDefineAutoCheckHandler implements ParserConfig.AutoTypeCheckHandler{

        @Override
        public Class<?> handler(String typeName, Class<?> aClass, int feature) {
            if(StringUtils.isEmpty(typeName)){
                return null;
            }
            if(!typeName.startsWith(SUPPORT_TYPE_PACKAGE)){
                throw new JSONException("auto type is not support"+typeName);

            }
            return null;
        }
    }
}
