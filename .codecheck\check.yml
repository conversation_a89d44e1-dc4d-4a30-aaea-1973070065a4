#codeCheck
---
version: 2.0
params:
  #pom文件在这个路径下,空为当前目录
  - name: buildPath
    value: ""
  # 编译目录
  - name: buildCmd
    value: "mvn clean package -Dmaven.test.skip=true -gs settings.xml -s settings.xml"
tool_params:
  secsolar:
    compile_root_dir: ${buildPath}
    compile_script: ${buildCmd}

steps:
  #在代码仓下载后，代码检查启动前
  pre_codecheck:
    - checkout ### 检出当前源码库 单仓
    - sh:
        command: |
          cd $WORKSPACE/code/${buildPath}   ###在代码仓下载后，代码检查启动前
          ${buildCmd}
        effect_tool: spotbugs

#xray代码扫描检查引擎相关的一些配置
codeCBS:
  domain: huawei.com   ### 针对HW域名检查的配置，可不配置，默认检查huawei.com;如果要检查多个域名，可以用英文分号；隔开
  dbType: PostgreSQL        ### 针对sql规范扫描的配置，目前仅支持PostgreSQL、MySQL和openGauss三种数据库类型，不支持多选。