/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.domain.favorites.repository;

import com.huawei.it.csrc.domain.favorites.entity.CsFavoritesMenuEntity;

import java.util.List;

/**
 * 菜单收藏仓库接口
 *
 * <AUTHOR>
 * @since 2022-12-16 06:30:20
 */
public interface ICsFavoritesMenuRepo {
    /**
     * 菜单收藏列表查询
     *
     * @param csFavoritesMenuEntity csFavoritesMenuEntity
     * @return List<CsFavoritesMenuEntity>
     */
    List<CsFavoritesMenuEntity> findByField(CsFavoritesMenuEntity csFavoritesMenuEntity);

    /**
     * 菜单收藏新增
     *
     * @param csFavoritesMenuEntity csFavoritesMenuEntity
     * @return void
     */
    void save(CsFavoritesMenuEntity csFavoritesMenuEntity);
}
