<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.csrc.infrastructure.dao.IAttachmentExDao">
    <select id="findRqDocByIds" resultType="map">
        SELECT rq_doc as doc,id
        FROM tpl_excel_queue_t
        where id in
        <foreach item="item" index="index" collection="taskIds" open="(" close=")" separator=" , ">
            #{item,jdbcType=NUMERIC}
        </foreach>
    </select>

    <select id="findRqDocByImportId" resultType="com.huawei.it.csrc.domain.entity.ImportTaskEntity">
        SELECT task_id          as taskId,
               remark           as remark,
               file_name        as fileName,
               server_file_name as filePath
        FROM tpl_excel_import_task_t
        where task_id = #{taskId,jdbcType=NUMERIC}
    </select>

    <select id="findRqDocByExportId" resultType="com.huawei.it.csrc.domain.entity.ImportTaskEntity">
        SELECT id         as taskId,
               rc_tmpname as fileName,
               rq_doc     as filePath
        FROM tpl_excel_queue_t
        where id = #{taskId,jdbcType=NUMERIC}
    </select>

    <select id="findImportTaskList" resultType="com.huawei.it.csrc.infrastructure.vo.AttachmenteExtVO">
        SELECT tpl.task_id AS taskId,
        lp.item_name AS moduleName,
        tpl.operate_qty AS totalRecords,
        tpl.imported_qty AS importedRecords,
        tpl.import_status AS importAndExportStatus,
        tpl.created_by AS createdBy,
        tpl.creation_date AS creationDate,
        tpl.file_name AS fileName,
        tpl.error_msg AS errorMsg,
        tpl.run_start_time AS runStartTime,
        tpl.run_end_time AS runEndTime,
        tpl.file_size AS fileSize,
        '1' as importExportFlag,
        lp.item_code as activity,
        lp.item_attr3 as taskType,
        tpl.server_file_name as sourceUrl,
        tpl.download_url as defSourceUrl,
        tpl.remark as defLogUrl,
        lp.item_attr4 as fileShowType
        FROM tpl_excel_import_task_t tpl,tpl_lookup_item_t lp,tpl_user_t u
        where tpl.type_id = lp.item_attr1
        and tpl.app_name = lp.item_attr2
        and lp.classify_code = 'LOOKUP_ACTIVITY_IMPORT' and lp.LANGUAGE = #{0.language}
        and tpl.created_by=u.user_id
        <include refid="search_IExcelImport_fragment"/>
        ORDER BY tpl.creation_date desc limit #{1.pageSize} OFFSET #{1.mysqlStartIndex}
    </select>

    <select id="findImportTaskListCount" resultType="int">
        SELECT COUNT(1)
        FROM tpl_excel_import_task_t tpl,tpl_lookup_item_t lp,tpl_user_t u
        where tpl.type_id = lp.item_attr1
        and tpl.app_name = lp.item_attr2
        and lp.classify_code = 'LOOKUP_ACTIVITY_IMPORT' and lp.LANGUAGE = #{0.language}
        and tpl.created_by=u.user_id
        <include refid="search_IExcelImport_fragment"/>
    </select>

    <select id="findExportTaskList" resultType="com.huawei.it.csrc.infrastructure.vo.AttachmenteExtVO">
        SELECT id AS taskId,
        lp.item_name AS moduleName,
        rq_flag AS importAndExportStatus,
        CASE WHEN rq_flag= '2' then rc_tmpname ELSE rq_doc END AS fileName,
        rq_date AS creationDate,
        rq_run_start_time AS runStartTime,
        rq_run_end_time AS runEndTime,
        filesize AS fileSize,
        records AS totalRecords,
        '2' as importExportFlag,
        lp.item_code as activity,
        tpl.userid as createdBy,
        lp.item_attr3 as taskType,
        tpl.rq_doc as sourceUrl,
        tpl.download_url as defSourceUrl,
        tpl.addlabelresult as defLogUrl,
        lp.item_attr4 as fileShowType
        FROM tpl_excel_queue_t tpl,tpl_lookup_item_t lp,tpl_user_t u
        where tpl.rc_module_name = lp.item_attr1
        and tpl.app_name = lp.item_attr2
        and lp.classify_code = 'LOOKUP_ACTIVITY_EXPORT'
        and lp.LANGUAGE = #{0.language}
        and tpl.userid=u.user_id
        <include refid="search_IExcelExport_fragment"/>
        ORDER BY tpl.rq_date desc
        limit #{1.pageSize} OFFSET #{1.mysqlStartIndex}
    </select>

    <select id="findExportTaskListCount" resultType="int">
        SELECT COUNT(1)
        FROM tpl_excel_queue_t tpl,tpl_lookup_item_t lp,tpl_user_t u
        where tpl.rc_module_name = lp.item_attr1
        and tpl.app_name = lp.item_attr2
        and lp.classify_code = 'LOOKUP_ACTIVITY_EXPORT'
        and lp.LANGUAGE = #{0.language}
        and tpl.userid=u.user_id
        <include refid="search_IExcelExport_fragment"/>
    </select>

    <sql id="search_IExcelImport_fragment">
        <if test='_parameter.get("0").activityList != null and !_parameter.get("0").activityList.isEmpty()'>
            AND tpl.type_id IN
            (
            select item_attr1 from tpl_lookup_item_t where classify_code = 'LOOKUP_ACTIVITY' and LANGUAGE = 'en_US'
            and item_code in
            <foreach collection='_parameter.get("0").activityList' item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        AND tpl.created_by = #{0.currentUserId}
        <if test='_parameter.get("0").fileName != null and _parameter.get("0").fileName !=""'>
            AND LOWER(tpl.file_name) LIKE LOWER(CONCAT('%' , TRIM(#{0.fileName}), '%'))
        </if>
        <choose>
            <when test='_parameter.get("0").status != null and _parameter.get("0").status !=""'>
                <choose>
                    <when test='_parameter.get("0").status == 1 '>
                        AND tpl.import_status='1'
                    </when>
                    <when test='_parameter.get("0").status == 2 '>
                        AND tpl.import_status='2'
                    </when>
                    <when test='_parameter.get("0").status == 3 '>
                        AND tpl.import_status='4'
                    </when>
                    <otherwise>
                        AND tpl.import_status='3'
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                AND tpl.import_status in ('1','2','3','4')
            </otherwise>
        </choose>
        <if test='_parameter.get("0").runStartTime != null and _parameter.get("0").runStartTime !=""'>
            AND tpl.creation_date &gt;= to_timestamp(#{0.runStartTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test='_parameter.get("0").runEndTime != null and _parameter.get("0").runEndTime !=""'>
            AND tpl.creation_date &lt;= to_timestamp(#{0.runEndTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
    </sql>

    <sql id="search_IExcelExport_fragment">
        <if test='_parameter.get("0").activityList != null and !_parameter.get("0").activityList.isEmpty()'>
            AND tpl.rc_module_name IN
            (
            select item_attr1 from tpl_lookup_item_t where classify_code = 'LOOKUP_ACTIVITY' and LANGUAGE = 'en_US'
            and item_code in
            <foreach collection='_parameter.get("0").activityList' item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        AND tpl.userid = #{0.currentUserId}
        <if test='_parameter.get("0").fileName != null and _parameter.get("0").fileName !=""'>
            AND (LOWER(tpl.rc_tmpname) LIKE LOWER(CONCAT('%' , TRIM(#{0.fileName}), '%'))
            or (tpl.iexcel_guid is not null and LOWER(tpl.rq_doc) LIKE LOWER(CONCAT('%' , TRIM(#{0.fileName}), '%'))))
        </if>
        <choose>
            <when test='_parameter.get("0").status != null and _parameter.get("0").status !=""'>
                <choose>
                    <when test='_parameter.get("0").status == 1 '>
                        AND tpl.rq_flag='1'
                    </when>
                    <when test='_parameter.get("0").status == 2 '>
                        AND tpl.rq_flag='2'
                    </when>
                    <otherwise>
                        AND tpl.rq_flag='3'
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                AND tpl.rq_flag in ('1','2','3')
            </otherwise>
        </choose>
        <if test='_parameter.get("0").runStartTime != null and _parameter.get("0").runStartTime !=""'>
            AND tpl.rq_date &gt;= to_timestamp(#{0.runStartTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test='_parameter.get("0").runEndTime != null and _parameter.get("0").runEndTime !=""'>
            AND tpl.rq_date &lt;= to_timestamp(#{0.runEndTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
    </sql>
</mapper>