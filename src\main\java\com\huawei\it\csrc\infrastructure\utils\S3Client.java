/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.utils;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.SdkClientException;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.amazonaws.services.s3.transfer.Transfer;
import com.amazonaws.services.s3.transfer.TransferManager;
import com.amazonaws.services.s3.transfer.TransferManagerBuilder;
import com.amazonaws.services.s3.transfer.Upload;
import com.amazonaws.services.s3.transfer.internal.TransferManagerUtils;
import com.amazonaws.services.s3.transfer.model.UploadResult;
import com.amazonaws.util.IOUtils;

import org.apache.commons.logging.Log;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.concurrent.ExecutorService;

public class S3Client {
    private static final Logger log = LoggerFactory.getLogger(S3Client.class);
    private AmazonS3 amazonS3;
    private TransferManagerBuilder tmb;
    private TransferManager tm;

    public void init(String accessKey, String secretKey, String host) {
        AWSCredentials credentials = new BasicAWSCredentials(accessKey, secretKey);
        ClientConfiguration config = new ClientConfiguration();
        config.setProtocol(Protocol.HTTP);
        config.setSignerOverride("S3SignerType");
        config.setSocketTimeout(180000);
        this.amazonS3 = AmazonS3ClientBuilder.standard().withCredentials(new AWSStaticCredentialsProvider(credentials))
                .withClientConfiguration(config)
                .withEndpointConfiguration(
                        new AwsClientBuilder.EndpointConfiguration(host, Regions.US_EAST_1.getName()))
                .enablePathStyleAccess().build();
        final ExecutorService executorServiceS3 = TransferManagerUtils.createDefaultExecutorService();
        this.tmb = TransferManagerBuilder.standard().withExecutorFactory(() -> executorServiceS3)
                .withS3Client(this.amazonS3);
        this.tm = this.tmb.build();
        ExecutorService executorService = this.tmb.getExecutorFactory().newExecutor();
    }

    public UploadResult multipartUploadFile(String bucketName, Long id, String key, File file) {
        long uploadStart = System.currentTimeMillis();
        log.info("multipartUploadFile uploadStart:{}", uploadStart);
        log.info("S3Client multipartUploadFile bucketName:{} id:{} key:{}", bucketName, id, key);
        PutObjectRequest request = new PutObjectRequest(bucketName, key, file);
        Upload upload = this.tm.upload(request);
        Transfer.TransferState transferState = null;
        double percentTransferred = 0.0d;

        while (!upload.isDone()) {
            transferState = upload.getState();
            percentTransferred = upload.getProgress().getPercentTransferred();
            log.info("multipartUploadFile transferState:{} percentTransferred:{}", transferState, percentTransferred);
            if (Double.isNaN(percentTransferred)) {
                percentTransferred = 0.0;
            }

            log.info("multipartUploadFile while redis set end");

            try {
                Thread.sleep(100L);
            } catch (InterruptedException var16) {
                log.error("sleep occour InterruptedException");
            }
        }

        transferState = upload.getState();
        percentTransferred = upload.getProgress().getPercentTransferred();
        log.info("multipartUploadFile end transferState:{} percentTransferred:{}", transferState, percentTransferred);
        log.info("S3Client multipartUploadFile completed before.");
        long uploadEnd = 0L;
        if (Transfer.TransferState.Completed.equals(transferState)) {
            try {
                UploadResult uploadResult = upload.waitForUploadResult();
                uploadEnd = System.currentTimeMillis();
                log.info("S3Client multipartUploadFile upload success cost time:{}", uploadEnd - uploadStart);
                return uploadResult;
            } catch (InterruptedException var15) {
                throw new SdkClientException("multipartUploadFile waitForUploadResult InterruptedException.");
            }
        } else {
            uploadEnd = System.currentTimeMillis();
            log.info("S3Client multipartUploadFile upload fail cost time :{}", uploadEnd - uploadStart);
            return new UploadResult();
        }
    }

    public ObjectMetadata downloadStream(String bucketName, String key, OutputStream out) {
        GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, key);
        S3Object s3Object = this.amazonS3.getObject(getObjectRequest);
        ObjectMetadata objectMetadata = s3Object.getObjectMetadata();
        S3ObjectInputStream in = s3Object.getObjectContent();

        try {
            IOUtils.copy(in, out);
        } catch (IOException var12) {
            throw new SdkClientException(var12);
        } finally {
            IOUtils.release(in, (Log) null);
        }

        return objectMetadata;
    }

    public boolean deleteObject(String bucketName, String key) {
        log.info("S3Client deleteObject begin bucketName:{} key:{}", bucketName, key);

        try {
            this.amazonS3.deleteObject(bucketName, key);
        } catch (SdkClientException var4) {
            log.error("S3Client deleteObject bucketName:{} key:{} fail.", bucketName, key);
            return false;
        }

        log.info("S3Client deleteObject bucketName:{} key:{} success.", bucketName, key);
        return true;
    }
}
