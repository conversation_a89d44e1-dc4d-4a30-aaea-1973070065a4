/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.interfaces.facade;

import com.huawei.it.csrc.application.dto.CsFavoritesMenuInputDTO;
import com.huawei.it.csrc.application.dto.CsFavoritesMenuOutputDTO;
import com.huawei.it.csrc.application.dto.CsFavoritesMenuSaveDTO;
import com.huawei.it.csrc.application.dto.MessageResultDTO;

import java.util.List;

import org.springframework.web.bind.annotation.RequestBody;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * 菜单收藏接口
 *
 * <AUTHOR>
 * @since 2022-12-16 06:30:20
 */
@Path("/v1/csFavorites")
@Produces(MediaType.APPLICATION_JSON)
public interface ICsFavoritesMenuFacade {
    /**
     * 菜单收藏列表查询
     *
     * @param csFavoritesMenuInputDto csFavoritesMenuInputDto
     * @return MessageResultDTO<List < CsFavoritesMenuOutputDTO>>
     */
    @POST
    @Path("/list")
    MessageResultDTO<List<CsFavoritesMenuOutputDTO>> findByField(
        @RequestBody CsFavoritesMenuInputDTO csFavoritesMenuInputDto);

    /**
     * 新增菜单收藏
     *
     * @param csFavoritesMenuSaveDto csFavoritesMenuSaveDto
     * @return MessageResultDTO
     */
    @POST
    @Path("")
    MessageResultDTO save(@RequestBody CsFavoritesMenuSaveDTO csFavoritesMenuSaveDto);
}