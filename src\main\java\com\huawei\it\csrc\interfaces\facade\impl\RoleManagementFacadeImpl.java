/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.interfaces.facade.impl;

import com.huawei.it.csrc.application.dto.response.BaseResponse;
import com.huawei.it.csrc.application.service.IRoleManagementAppService;
import com.huawei.it.csrc.interfaces.facade.IRoleManagementFacade;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.request.RequestConstants;
import com.huawei.it.jalor5.security.RoleVO;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.IOException;

/**
 * 作业岛角色查询创建
 *
 * <AUTHOR>
 * @since 2024-04-24 09:58
 */
@Named
@JalorResource(code = "RoleManagementFacadeImpl", desc = "作业岛角色查询创建")
public class RoleManagementFacadeImpl implements IRoleManagementFacade {

    @Inject
    private IRoleManagementAppService iRoleManagementAppService;

    @Override
    @Audit
    @JalorOperation(code = RequestConstants.READ, desc = RequestConstants.READ_DESC)
    public BaseResponse<RoleVO> findOrCreateRole(String roleName, String scope) {
        RoleVO roleVO;
        try {
            roleVO = iRoleManagementAppService.findOrCreateRole(roleName, scope);
            return BaseResponse.success(roleVO);
        } catch (ApplicationException | IOException e) {
            e.printStackTrace();
            return BaseResponse.filed();
        }
    }
}
