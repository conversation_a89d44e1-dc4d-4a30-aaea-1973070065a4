/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.repository.impl;

import com.huawei.it.csrc.domain.entity.ImportTaskEntity;
import com.huawei.it.csrc.domain.repository.IAttachmentRepo;
import com.huawei.it.csrc.infrastructure.dao.IAttachmentExDao;
import com.huawei.it.csrc.infrastructure.vo.AttachmenteExtVO;
import com.huawei.it.csrc.infrastructure.vo.ImportAndExportVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.inject.Named;

/**
 * 根据导出任务ID获取下载链接
 *
 * @since 2022-08-19
 */
@Named
public class AttachmentRepoImpl implements IAttachmentRepo {
    @Resource
    private IAttachmentExDao attachmentExDao;

    @Override
    public Map<Long, String> findRqDocByIds(List<Long> taskIds) throws ApplicationException {
        Map<Long, String> backMap = new HashMap<>();
        List<Map<String, Object>> mapList = attachmentExDao.findRqDocByIds(taskIds);
        if (mapList != null && mapList.size() > 0) {
            for (int i = 0; i < mapList.size(); i++) {
                Map<String, Object> map = mapList.get(i);
                backMap.put(Long.valueOf(map.get("id").toString()), String.valueOf(map.get("doc")));
            }
        }
        return backMap;
    }

    @Override
    public ImportTaskEntity findRqDocByImportId(Long taskId) throws ApplicationException {
        return attachmentExDao.findRqDocByImportId(taskId);
    }

    @Override
    public ImportTaskEntity findRqDocByExportId(Long taskId) throws ApplicationException {
        return attachmentExDao.findRqDocByExportId(taskId);
    }

    @Override
    public PagedResult<AttachmenteExtVO> findImportTaskList(ImportAndExportVO var1, PageVO var2)
        throws ApplicationException {
        return attachmentExDao.findImportTaskList(var1, var2);
    }

    @Override
    public PagedResult<AttachmenteExtVO> findExportTaskList(ImportAndExportVO var1, PageVO var2)
        throws ApplicationException {
        return attachmentExDao.findExportTaskList(var1, var2);
    }
}
