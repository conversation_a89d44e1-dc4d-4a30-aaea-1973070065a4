/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.dao;

import com.huawei.it.csrc.domain.entity.CsRecentlyUsedEntity;
import com.huawei.it.csrc.infrastructure.po.CsRecentlyUsedPO;

import java.util.List;

import org.apache.ibatis.annotations.Param;

/**
 * 最近使用功能仓库接口
 *
 * <AUTHOR>
 * @since 2022-12-13 10:19:04
 */
public interface ICsRecentlyUsedDao {
    void dealToggleMenus(CsRecentlyUsedPO po);

    List<CsRecentlyUsedPO> findByField(@Param("recentlyUsedEntity") CsRecentlyUsedEntity recentlyUsedEntity);

    List<String> getRoleDataAccess();
}
