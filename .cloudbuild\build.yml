---
version: 2.0

#构建环境
env:
  label: BPIT_Build_Default

#构建参数，构建命令可通过环境变量使用
params:
  - name: product
    value: cloudbuild2.0

#构建步骤
steps:
  PRE_BUILD: # 构建准备:下载代码
    - checkout
  BUILD: # 构建执行
    - build_execute:
        # 构建命令，如sh build.sh 或 make
        command: sh build.sh
        check:
          mode: sync
          buildcheck:
            ruleset: default #配置后启用问题屏蔽能力
            task_name: ${CLOUD_BUILD_PROJECT_NAME}_buildcheck
            exclude_dir: ./pom.xml;./build.sh;./script/build-mvn.sh  #**/pom.xml;**/start.sh;**/dbmanager/;buildInfo.properties;/devcloud/ws/;package
            project_dir: ./
          sourcecheck: # 构建来源检查，非必配项，不配时不检查
            - project_type: maven  # 必配项。当前只支持检查maven仓库来源
              project_dir: ./  # 工程根目录，必配项。如maven为pom文件所在目录
              settings_xml: $WORKSPACE/settings.xml  #非必配项，不配时默认使用执行机器的settings文件配置，maven工程自定义settings文件属性，当前只支持maven类型。支持${WORKSPACE}相对路径，如果路径比较复杂，建议使用。
          dependency:
            #必配项，选择对应的编译类型
            - tool_type: maven
              #必配项。如maven为pom文件所在目录
              project_dir: ./
              settings_xml: $WORKSPACE/settings.xml  #非必配项，不配时默认使用执行机器的settings文件配置，maven工程自定义settings文件属性，当前只支持maven类型。支持${WORKSPACE}相对路径，如果路径比较复杂，建议使用。
              # 非必配项，默认为false。配置为true时，不进行插件依赖检查，当前插件检查依赖只支持Maven类型
              skip_plugin: true

  POST_BUILD:  # 构建后
    - sh:
        command: | #数字签名配置
          signclient "target/*.jar" #不同签名包之间用分号分隔
    - artget: #从云龙上传构建cloudArtifact仓库 需要云龙流水线传入serviceId,serviceName,isRelease参数
        artifact_type: cloudartifact  # 仓库类型
        action: push #选填。默认值为push,当上传包数量超过10个时，必填。
        file_path: "package,target/*.jar;cms,target/*.jar.cms" #上传包的路径是相对路径，相对于workspace
        version_output_path: .  #必填，用于策略验收报告获取可信构建检查结果
    #- upload_cloud_artifact: #发布到云龙cloudArtifact(云龙部署专用)
    #    file_path: opt.fsp.hq.parent/fsp.hq.server/target/fsp_hq_service_*.jar
    - compile_report:
        complier: javac
        rules:
          - 'error /.*\[ERROR\].*/'
          - 'warning /.*\[WARNING\].*(?!Checksum validation failed).*\n.*(?!No processor claimed any of these annotations).*\n.*(?!The metadata).*\n.*(?!is invalid: input contained no data).*\n.*(?!Maven will be executed).*\n.*(?!Ignoring unrecognized).*\n.*(?!No processor claimed any of these annotations).*\n.*(?!Failed to read extensions descriptor).*/'
    - version_set #记录云龙version_set(云龙部署专用)