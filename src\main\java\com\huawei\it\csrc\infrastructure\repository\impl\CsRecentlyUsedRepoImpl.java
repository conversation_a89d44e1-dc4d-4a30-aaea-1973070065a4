/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.repository.impl;

import com.huawei.it.csrc.domain.entity.CsRecentlyUsedEntity;
import com.huawei.it.csrc.domain.repository.ICsRecentlyUsedRepo;
import com.huawei.it.csrc.infrastructure.convert.CsRecentlyUsedConvertEntityAndPo;
import com.huawei.it.csrc.infrastructure.dao.ICsRecentlyUsedDao;
import com.huawei.it.csrc.infrastructure.po.CsRecentlyUsedPO;
import com.huawei.it.csrc.infrastructure.utils.RequestContextUtils;
import com.huawei.it.csrc.infrastructure.utils.SnowflakeIdWorkerUtil;
import com.huawei.it.csrc.infrastructure.utils.TranslationUtils;
import com.huawei.it.jalor5.core.request.impl.RequestContext;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;

import javax.inject.Inject;
import javax.inject.Named;

/**
 * 最近使用功能仓库接口实现
 *
 * <AUTHOR>
 * @since 2022-12-13 10:19:04
 */
@Named
public class CsRecentlyUsedRepoImpl implements ICsRecentlyUsedRepo {
    @Value("${cs.recently.used.showRow}")
    private int showRow;

    @Inject
    private ICsRecentlyUsedDao csRecentlyUsedDao;

    @Inject
    private CsRecentlyUsedConvertEntityAndPo csRecentlyUsedConvert;

    /**
     * 最近使用功能列表查询
     *
     * @param csRecentlyUsedEntity csRecentlyUsedEntity
     * @return List<CsRecentlyUsedEntity>
     */
    @Override
    public List<CsRecentlyUsedEntity> findByField(CsRecentlyUsedEntity csRecentlyUsedEntity) {
        List<CsRecentlyUsedEntity> result = new ArrayList<>();
        Long createdBy = RequestContextUtils.getCurUser().getUserId();
        String language = RequestContext.getCurrent().getUserLanguage();
        csRecentlyUsedEntity.setCreatedBy(createdBy);
        csRecentlyUsedEntity.setShowRow(showRow);
        List<CsRecentlyUsedPO> listCsRecentlyUsedPo = csRecentlyUsedDao.findByField(csRecentlyUsedEntity);
        if (listCsRecentlyUsedPo != null) {
            result = listCsRecentlyUsedPo.stream().map((po) -> {
                // name翻译
                po.setName(TranslationUtils.translationName(language, po.getName()));
                return csRecentlyUsedConvert.convertToEntity(po);
            }).collect(Collectors.toList());
        }
        return result;
    }

    /**
     * 最近使用功能新增
     *
     * @param csRecentlyUsedEntity csRecentlyUsedEntity
     * @return void
     */
    @Override
    public void save(CsRecentlyUsedEntity csRecentlyUsedEntity) {
        CsRecentlyUsedPO savePO = csRecentlyUsedConvert.convertToPO(csRecentlyUsedEntity);
        Long createdBy = RequestContextUtils.getCurUser().getUserId();
        savePO.setCreatedBy(createdBy);
        savePO.setRecentlyUsedId(SnowflakeIdWorkerUtil.generateId());
        savePO.setId(SnowflakeIdWorkerUtil.generateId());
        csRecentlyUsedDao.dealToggleMenus(savePO);
    }

    @Override
    public List<String> getRoleDataAccess() {
        return csRecentlyUsedDao.getRoleDataAccess();
    }
}
