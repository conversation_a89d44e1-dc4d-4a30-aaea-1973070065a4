/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.domain.service.impl;


import com.huawei.it.csrc.domain.service.IProgramManagementDomainService;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.util.JsonUtil;
import com.huawei.it.jalor5.security.ProgramVO;
import com.huawei.it.jalor5.security.UserVO;
import com.huawei.it.jalor5.security.service.IProgramService;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.IOException;
import java.util.Objects;

/**
 * 查找或创建数据范围
 *
 * <AUTHOR>
 * @since 2024-06-27 15:02
 */
@Named
public class ProgramManagementDomainService implements IProgramManagementDomainService {
    @Inject
    private IProgramService programService;

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRES_NEW)
    public ProgramVO findOrCreateProgram(ProgramVO queryProgram) throws ApplicationException, IOException {
        ProgramVO programVO;

        programVO = programService.findProgram(queryProgram);
        if (Objects.nonNull(programVO)) {
            return programVO;
        }

        // 没有数据范围，就创建
        String objectToJson = JsonUtil.objectToJson(queryProgram);
        ProgramVO createProgramVO = JsonUtil.stringToObject(objectToJson, ProgramVO.class);
        UserVO userVO = new UserVO();
        userVO.setUserId(RequestContext.getCurrent().getUser().getUserId());
        userVO.setUserCN(RequestContext.getCurrent().getUser().getUserCN());
        createProgramVO.setOwner(userVO);
        createProgramVO.setCreatedBy(userVO.getUserId());
        createProgramVO.setLastUpdatedBy(userVO.getUserId());
        createProgramVO.setStatus(1);
        createProgramVO.setApplyStatus(1);
        programService.createProgram(createProgramVO);
        programVO = programService.findProgram(queryProgram);
        return programVO;
    }
}
