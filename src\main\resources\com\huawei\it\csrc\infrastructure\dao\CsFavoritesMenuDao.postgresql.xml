<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.csrc.infrastructure.dao.ICsFavoritesMenuDao">
    
    <resultMap type="com.huawei.it.csrc.infrastructure.po.CsFavoritesMenuPO" id="resultMap">
            <result property="id" column="id"/>
            <result property="favoritesMenuId" column="favorites_menu_id"/>
            <result property="createdBy" column="created_by"/>
            <result property="siteNodeId" column="site_node_id"/>
            <result property="applicationId" column="application_id"/>
            <result property="tag" column="tag"/>
            <result property="creationDate" column="creation_date"/>
            <result property="lastUpdateDate" column="last_update_date"/>
            <result property="lastUpdateBy" column="last_update_by"/>
            <result property="deleteFlag" column="delete_flag"/>
            <result property="tenantId" column="tenant_id"/>
            <result property="url" column="url"/>
            <result property="name" column="name"/>
    </resultMap>

    <sql id="tableName">
        cs_favorites_menu_t
    </sql>

    <sql id="allFields">
            id,
            favorites_menu_id,
            created_by,
            site_node_id,
            application_id,
            tag,
            creation_date,
            last_update_date,
            last_update_by,
            delete_flag,
            tenant_id
    </sql>
    <sql id="allFieldsWithoutId">
            favorites_menu_id,
            created_by,
            site_node_id,
            application_id,
            tag,
            creation_date,
            last_update_date,
            last_update_by,
            delete_flag,
            tenant_id
    </sql>

    <sql id="allValuesWithoutId">
            #{favoritesMenuId,jdbcType=BIGINT},
            #{createdBy,jdbcType=BIGINT},
            #{siteNodeId,jdbcType=BIGINT},
            #{applicationId,jdbcType=BIGINT},
            1,
            #{creationDate,jdbcType=TIMESTAMP},
            #{lastUpdateDate,jdbcType=TIMESTAMP},
            #{lastUpdateBy,jdbcType=BIGINT},
            #{deleteFlag,jdbcType=VARCHAR},
            #{tenantId,jdbcType=VARCHAR}
    </sql>
    
    <sql id="uniqueKeyField">
        id=#{id,jdbcType=BIGINT}
    </sql>

    <sql id="setValues">
            <if test='favoritesMenuId != null'>
                favorites_menu_id = #{favoritesMenuId,jdbcType=BIGINT},
            </if>
            <if test='createdBy != null'>
                created_by = #{createdBy,jdbcType=BIGINT},
            </if>
            <if test='siteNodeId != null'>
                site_node_id = #{siteNodeId,jdbcType=BIGINT},
            </if>
            <if test='applicationId != null'>
                application_id = #{applicationId,jdbcType=BIGINT},
            </if>
            <if test='tag != null'>
                tag = #{tag,jdbcType=INTEGER},
            </if>
            <if test='creationDate != null'>
                creation_date = #{creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test='lastUpdateDate != null'>
                last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
            </if>
            <if test='lastUpdateBy != null'>
                last_update_by = #{lastUpdateBy,jdbcType=BIGINT},
            </if>
            <if test='deleteFlag != null'>
                delete_flag = #{deleteFlag,jdbcType=VARCHAR},
            </if>
            <if test='tenantId != null'>
                tenant_id = #{tenantId,jdbcType=VARCHAR},
            </if>
    </sql>

    <sql id="searchFields">
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='createdBy != null'>
                AND created_by=#{createdBy,jdbcType=BIGINT}
            </if>
            <if test='applicationId != null'>
                AND application_id=#{applicationId,jdbcType=BIGINT}
            </if>
            <if test='tag != null'>
                AND tag=#{tag,jdbcType=INTEGER}
            </if>
        </trim>
    </sql>

    <select id="findByField" resultMap="resultMap">
        SELECT
        cata.id AS site_node_id,
        cata.url AS url,
        cata."name" AS NAME
        FROM
            cs_favorites_menu_t us,tpl_catalog_t cata
        WHERE us.site_node_id=cata.id
        AND EXISTS (
        SELECT
        1
        FROM
        tpl_user_role_program_t rol,
        tpl_tenant_role_acl_t acl,
        tpl_function_catalog_t fuc
        WHERE
        rol.role_id = acl.role_id
        AND acl.function_id = fuc.function_id
        AND rol.user_id = us.created_by
        AND fuc.catalog_id = cata.id
        AND rol.org_code = #{favoritesMenuPO.applicationIdStr,jdbcType=VARCHAR}
        AND rol.user_id = #{favoritesMenuPO.createdBy,jdbcType=BIGINT}
        )
        and us.tag=1
        and us.application_id = #{favoritesMenuPO.applicationId,jdbcType=INTEGER}
        AND us.created_by = #{favoritesMenuPO.createdBy,jdbcType=BIGINT}
        order by us.last_update_date desc
    </select>

    <insert id="dealFavoritesMenu" parameterType="com.huawei.it.csrc.infrastructure.po.CsFavoritesMenuPO">
        INSERT INTO <include refid="tableName"/>
        (<include refid="allFieldsWithoutId"/>)
        VALUES
        (<include refid="allValuesWithoutId"/>)
        ON CONFLICT (created_by,site_node_id,application_id)
        DO UPDATE SET last_update_date=NOW(),tag=(select -mm.tag from cs_favorites_menu_t mm where
            mm.created_by =#{createdBy,jdbcType=BIGINT}
            and mm.site_node_id= #{siteNodeId,jdbcType=BIGINT}
            and application_id = #{applicationId,jdbcType=BIGINT}
            )
    </insert>
</mapper>
