/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.po;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * The PO of CsRecentlyUsed
 *
 * <AUTHOR>
 * @since 2022-12-13 10:19:04
 */
@Data
public class CsRecentlyUsedPO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 最近使用ID
     */
    private Long recentlyUsedId;

    /**
     * 创建人
     */
    private Long createdBy;

    /**
     * 菜单ID
     */
    private Long siteNodeId;

    /**
     * 作业岛ID:作业岛ID，作业岛的唯一身份标识
     */
    private Long applicationId;

    /**
     * 创建时间
     */
    private Date creationDate;

    /**
     * 上次更新时间
     */
    private Date lastUpdateDate;

    /**
     * 最后更新人
     */
    private Long lastUpdatedBy;

    /**
     * 删除标识:删除标识:用于表示数据是否软删除，删除后一般业务用户不可见；Y-已删除，N-未删除
     */
    private String deleteFlag = "N";

    /**
     * 租户id
     */
    private String tenantId = "-1";

    /**
     * 跳转链接
     */
    private String url;

    /**
     * 菜单名
     */
    private String name;
}
