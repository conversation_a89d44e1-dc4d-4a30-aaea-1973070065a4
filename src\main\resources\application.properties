#应用配置
application.enableMulti=true
application.appName=opt_fsp_cs
application.scope=otc
application.serviceVersion=1.0
application.appId=com.huawei.finance.ai.opt.common
application.subAppId=opt_fsp_cs

#配置文件profile
spring.profiles.active=${spring_profiles_active:dev}

#端口号，上下文配置
server.port=8003
dev.server.port=60000
server.servlet.context-path=/opt/fsp/cs
## ----公服访问微服务时，如何获取url，默认true，使用注册应用时指定的url-------
app.url.enabled=false
security.registration.privatekey=


#连接池最大连接数，默认值为8，根据应用实际访问量设置，一般设置100， HIC配置无此设置
datasource.maxActive.1=100
#连接池中最大的空闲的连接数，超过的空闲连接将被释放，默认值为8，根据应用实际访问量设置，一般设置为15（HIC配置：最大连接数）
datasource.maxIdle.1=15
#连接池中最小的空闲的连接数，低于这个数量会被创建新的连接，默认值为0，根据应用实际访问量设置，一般设置为10（HIC配置：最小连接数）
datasource.minIdle.1=10
#最大等待时间，当没有可用连接时，连接池等待连接释放的最大时间，默认无限，调整为600000ms（HIC配置：最大等待时间（-1无限等待））
datasource.maxWaitMillis.1=600000
#超过removeAbandonedTimeout时间后，是否进行没用连接（废弃）的回收，默认为false，调整为true（HIC配置无此配置）
datasource.removeAbandonedOnBorrow.1=true
#超过removeAbandonedTimeout时间后，是否进行没用连接（废弃）的回收，默认为false，调整为true（HIC配置无此配置）
datasource.removeAbandoned.1=true
#超过时间限制，回收没有用（废弃）的连接（默认为300秒，调整为120）
datasource.removeAbandonedTimeout.1=120
#设置的Evict线程的时间，单位ms，大于0才会开启evict检查线程（HIC配置：连接回收时间间隔（毫秒））
datasource.timeBetweenEvictionRunsMillis.1=30000

#jwt开关，公私钥默认已经内置了，可以不用配置，如果需要自定义可以自己配置
security.enabled=true
jalor.jwtFilter.exclusions=*/servlet/rebuildSession

#开启页面访问配置
jalor.web.resource-filter.enabled=true

#注册中心地址
registry.eureka.serviceUrl.default=http://fin.register.his-beta.huawei.com/msa/register/v2
#注册中心环境，即group
usf.service.environment=sit2

#支持名称相同的bean的覆盖
spring.main.allow-bean-definition-overriding=true

#jalor.jwt-filter.enabled=false

jalor.defaultRejectionHandler.enabled=true
jalor.server.ssl.enabled=false
#jalor.auth.excludes=/
welink.event.open-encrypt-server=false

jalor.web.csrf-filter.enabled=false
#jalor.jwt-filter.disabled=true
spring.mvc.formcontent.filter.enabled=false
jalor.auth.excludes=/jalorSaasSSo/services/jalor/demo/test/none,/jalorSaasSSo/services/jalor/saas/v1/query/tenant/operation/Service/jalorSaas,/jalorSaasSSo/services/jalor/saas/v1/tenant/findUserOperationList/Service/jalorSaas

jalor.n10n.redis.pub.topic=saasTopic
jalor.saas.start.loadCache.enabled=false
App.Security.RefererExclusions=*/services/jalor/registry/*
#跳过校验负载
jalor.jwtVerify.skipAudienceIssuer=true

# 审计日志开关
jalor.auditlog.enabled=true
#审计日志输出到数据库
jalor.auditlog.destination=db,file



#开启用户在线管理功能
jalor.web.useronline-filter.enabled=true

s3.pdmcore.host=s3.pdmcore.host
jalor.store.s3.endpoint=s3-kp-kwe.his-beta.huawei.com
jalor.store.s3.bucketname=csrc-dev01

jalor.store.s3.ak=
jalor.store.s3.sk=
s3.journalBucketName=citc-sit
s3.csrc.accessKey=
s3.csrc.secretKey=

## 开启定制功能集--不推荐
jalor.function.custom.enable=true

# 人员控件查询内网
fetch.intra.user=true

cs.recently.used.showRow=10

aes.gcm.workKey=
aes.gcm.first=
aes.gcm.salt=
aes.gcm.second=
aes.gcm.third=
aes.hmac.workKey=
Jalor.Security.HostWhiteList.Disabled=true

j2c.idaas.user=hqsp_a26641245c514c88842535917954e7f5
j2c.idaas.password=
idaas.appConfUrl=https://uniportal-beta.huawei.com/pubconf/config/client/v1/sdkconf
jalor5.web.ssoFilter.exclusions=*\\.js,*\\.jpg,*\\.png,*\\.gif,*\\.bmp,*\\.css,*\\.class,*\\.properties,*/initUserPermission,*/rpcInitUserPermission,*/registryQueryService/*,*/personalized/setting/list,*/helperprocxy/*,*/logService/*,*/findCurrentUserThemeSettings/*,*/tryFindOrCreateUser/*,*/eureka/serverext/*,*/jalor/security/mypermissionquery/*,*/auditLog/createAuditLog,*/publicservices/*,*/jalor/eureka/serverext/list,*/servlet/cache,*/servlet/userCacheClean,*/servlet/health,*/session/invalidated

security.group.publickey=
security.group.privatekey=
security.jwt.group.alg=HS512
security.jwt.gateway.alg=RS512
security.gateway.publickey=


application.dbType=postgresql
datasource.jdbcDriverClass.1=org.postgresql.Driver
datasource.url.1=jdbc:postgresql://*************:5432/finance_ai_opt?stringtype=unspecified&currentSchema=opt_fsp_cs1
datasource.user.1=dmaiopt
datasource.password.1=
datasource.name.1=opt_fsp_cs
enterprise=-1

#租户默认角色是否启用, 默认不启用
jalor.saas.tenant.defaultRole.enable=true
#租户默认角色的类型 CUSTOM / COMMON，角色类型：COMMON：通用角色；CUSTOM：租户自定义角色
jalor.saas.tenant.defaultRole.RoleType=COMMON
#租户默认角色编码
jalor.saas.tenant.defaultRole.RoleCode=TenantAdmin
#租户默认角色的名称
jalor.saas.tenant.defaultRole.RoleName=TenantAdmin

log.filter.ctrl=true
jalor.log.error.desensitized.enabled=true

#日志脱敏
sensitive.field.list.regexp=(accessKey|secretKey|host|canonicalPath|WebContainer|path|file|clientip|properties|MultivaluedMap|addressInfo|env|token|Environment|extensionField2|slaves|port|ProtocolHandler|from|Master|connections initialized for|master|LB|connect|12|connectorUrls|address|channel|servers|url)(.{1,50}(?=accessKey|secretKey|host|canonicalPath|WebContainer|path|file|clientip|properties|MultivaluedMap|addressInfo|env|token|Environment|extensionField2|slaves|port|ProtocolHandler|from|Master|connections initialized for|master|LB|connect|12|connectorUrls|address|channel|servers|url)|.{1,50})
xml.constants=http://javax.xml.XMLConstants/features/secure-processing

jalor.internal.service.url.list=POST$/jalor/attachment/createAttachmentWithFilePath,GET$/jalor/syncoperation/syncOperationMsg/sendSyncOperationMsg,POST$/jalor/syncoperation/syncOperationMsg/pullExceptionMsg,POST$/jalor/logs/auditLog/asyncCreateLog,POST$/jalor/logs/auditLog/createAuditLog,POST$/jalor/logs/auditLog/createAuditLogList,POST$/jalor/logs/auditLog/createLog,POST$/jalor/logs/auditLog/createLogList,POST$/jalor/auditLog/auditLog/createAuditLog,POST$/jalor/auditLog/auditLog/createAuditLogList,POST$/jalor/auditLog/auditLog/createLog,POST$/jalor/auditLog/auditLog/asyncCreateLog,POST$/jalor/auditLog/auditLog/createLogList,GET$/jalor/eureka/serverext/list,POST$/jalor/excel/import/task,GET$/jalor/task/import/list/page/monitor/{pageSize}/{curPage},GET$/jalor/task/import/get/findImportTaskById,POST$/jalor/task/import/update/task,POST$/jalor/task/import/create/task,POST$/jalor/task/export/createExportTask,GET$/jalor/task/export/list/page/monitor/{pageSize}/{curPage},GET$/servlet/cache,POST$/servlet/cache,GET$/servlet/role,POST$/servlet/userCacheClean,GET$/servlet/userCacheClean,GET$/jalor/task/export/findExportTaskById,POST$/jalor/task/export/find/findTaskList4Export,GET$/jalor/task/export/findExportTaskByGuid,POST$/jalor/task/import/get/findImportTask,POST/jalor/task/import/findImporttaskByHaedoc
