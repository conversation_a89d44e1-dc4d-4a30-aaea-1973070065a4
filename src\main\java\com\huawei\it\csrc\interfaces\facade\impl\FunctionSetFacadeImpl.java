/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
 */

package com.huawei.it.csrc.interfaces.facade.impl;

import com.huawei.it.csrc.application.service.IFunctionSetAppService;
import com.huawei.it.csrc.base.vo.MessageResultVO;
import com.huawei.it.csrc.interfaces.facade.IFunctionSetFacade;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.request.RequestConstants;

import javax.annotation.Resource;
import javax.inject.Named;

/**
 * 功能描述：进行中任务权限校验
 *
 * @since 2022-08-24
 */
@Named
@JalorResource(code = "FunctionSetFacadeImpl", desc = "Function Set FacadeImpl")
public class FunctionSetFacadeImpl implements IFunctionSetFacade {
    @Resource
    IFunctionSetAppService functionSetAppService;

    /**
     * 查询Count
     *
     * @param userId userId
     * @param functionName functionName
     * @return MessageResultVO
     * @throws ApplicationException
     */
    @Override
    @JalorOperation(code = RequestConstants.READ, desc = RequestConstants.READ_DESC)
    @Audit(module = "Program Management", operation = "functionSetFacade.getFunctionCount")
    public MessageResultVO<Integer> getFunctionCount(long userId, String functionName) throws ApplicationException {
        return functionSetAppService.getFunctionCount(userId, functionName);
    }
}
