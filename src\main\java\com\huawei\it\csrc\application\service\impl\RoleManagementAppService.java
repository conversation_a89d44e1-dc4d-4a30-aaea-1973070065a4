/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.application.service.impl;

import com.huawei.it.csrc.application.service.IRoleManagementAppService;
import com.huawei.it.csrc.domain.service.IRoleManagementDomainService;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.security.RoleVO;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.IOException;

/**
 * 查找或者创建角色
 *
 * <AUTHOR>
 * @since 2024-04-24 10:02
 */
@Named
public class RoleManagementAppService implements IRoleManagementAppService {
    @Inject
    private IRoleManagementDomainService iRoleManagementDomainService;
    @Override
    public RoleVO findOrCreateRole(String roleName, String scope) throws ApplicationException, IOException {
        RoleVO queryRoleVO = new RoleVO();
        queryRoleVO.setRoleName(roleName);
        queryRoleVO.setScope(scope);
        queryRoleVO.setRoleId(0);
        queryRoleVO.setTenantId("-1");
        return iRoleManagementDomainService.findOrCreateRole(queryRoleVO);
    }
}
