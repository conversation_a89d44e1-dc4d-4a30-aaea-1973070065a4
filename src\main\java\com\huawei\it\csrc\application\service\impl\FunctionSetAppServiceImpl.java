/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.application.service.impl;

import com.huawei.it.csrc.application.service.IFunctionSetAppService;
import com.huawei.it.csrc.base.vo.MessageResultVO;
import com.huawei.it.csrc.domain.service.IFunctionSetDomainService;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import javax.inject.Inject;
import javax.inject.Named;

/**
 * 功能描述：进行中任务权限校验
 *
 * @since 2022-08-24
 */
@JalorResource(code = "FunctionSetAppServiceImpl", desc = "FunctionSetAppServiceImpl")
@Named
public class FunctionSetAppServiceImpl implements IFunctionSetAppService {
    @Inject
    private IFunctionSetDomainService domainService;

    @Override
    public MessageResultVO<Integer> getFunctionCount(long userId, String functionName) throws ApplicationException {
        return MessageResultVO.success(domainService.getFunctionCount(userId, functionName));
    }
}