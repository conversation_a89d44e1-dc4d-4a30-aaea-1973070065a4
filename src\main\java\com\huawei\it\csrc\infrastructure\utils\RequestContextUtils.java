/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.csrc.infrastructure.utils;

import com.huawei.it.jalor.core.saas.tenant.TenantIdUtil;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.IUserPrincipal;
import com.huawei.it.jalor5.core.request.RequestContextException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;

import java.util.Optional;

public class RequestContextUtils {
    public static String getTenantId() {
        return TenantIdUtil.getTenantId();
    }

    public static Long getApplicationId() {
        Long applicationId = (Long) getCurrent().getItem("ApplicationId");
        if (applicationId == null) {
            applicationId = AppIslandUtils.getApplicationId();
            getCurrent().setItem("ApplicationId", applicationId);
        }

        return applicationId;
    }

    public static IRequestContext getCurrent() {
        IRequestContext current = RequestContextManager.getCurrent(true);
        if (current == null) {
            RequestContextManager.buildBasicContext();
            current = RequestContextManager.getCurrent(true);
        }

        return current;
    }

    public static Long getCurUserId() {
        return getCurUser().getUserId();
    }

    public static UserVO getCurUser() {
        IUserPrincipal user = RequestContext.getCurrent().getUser();
        return (UserVO) Optional.ofNullable(user).orElseThrow(RequestContextException::new);
    }
}
