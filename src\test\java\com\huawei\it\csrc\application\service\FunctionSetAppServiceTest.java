package com.huawei.it.csrc.application.service;

import com.huawei.it.csrc.application.service.impl.FunctionSetAppServiceImpl;
import com.huawei.it.csrc.base.vo.MessageResultVO;
import com.huawei.it.csrc.domain.service.IFunctionSetDomainService;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class FunctionSetAppServiceTest extends BaseTest {

    @InjectMocks
    private FunctionSetAppServiceImpl functionSetAppServiceImpl = new FunctionSetAppServiceImpl();

    @Mock
    private IFunctionSetDomainService domainService;

    /**
     * Init mock.
     */
    @Before
    public void initMock() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * findPagedList
     *
     * @throws ApplicationException the application exception
     */
    @Test
    public void getFunctionCount() throws ApplicationException {
        long userId = 131269511L;
        String functionName = "aa";
        MessageResultVO<Integer> result = functionSetAppServiceImpl.getFunctionCount(userId, functionName);
        Assert.assertNotNull(result);
    }

}

