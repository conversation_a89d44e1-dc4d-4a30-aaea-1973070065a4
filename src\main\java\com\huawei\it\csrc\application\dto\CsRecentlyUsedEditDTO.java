/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.application.dto;

import java.io.Serializable;
import lombok.Data;

/**
 * The DTO of CsRecentlyUsed For Update
 *
 * <AUTHOR>
 * @since 2022-12-13 10:19:04
 */
@Data
public class CsRecentlyUsedEditDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 最近使用ID
     */
    private Long recentlyUsedId;

    /**
     * 创建人
     */
    private Long createdBy;

    /**
     * 菜单ID
     */
    private Long siteNodeId;

    /**
     * 最后更新人
     */
    private Long lastUpdatedBy;

    /**
     * 删除标识:删除标识:用于表示数据是否软删除，删除后一般业务用户不可见；Y-已删除，N-未删除
     */
    private String deleteFlag;
}
