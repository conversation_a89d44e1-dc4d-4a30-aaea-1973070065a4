<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.csrc.infrastructure.dao.RolePermissionAndSiteNodeDao">

    <resultMap type="com.huawei.it.csrc.infrastructure.po.AclentryPO" id="entryMap">
        <result property="id" column="id"/>
        <result property="roleId" column="role_id"/>
        <result property="permissionId" column="permission_id"/>
        <result property="resourceType" column="resource_type"/>
        <result property="siteNodeId" column="site_node_id"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdateDate" column="last_update_date"/>


    </resultMap>

    <sql id="allFields">
        id,
        role_id,
        permission_id,
        resource_type,
        site_node_id,
        creation_date,
        created_by,
        last_update_date
    </sql>
    <select id="findPermissionAndSiteNodeIdByRoleId" resultMap="entryMap">
        SELECT
            <include refid="allFields"/>
        FROM
            tpl_aclentry_t
        WHERE
        role_id=#{roleId,jdbcType=INTEGER}
    </select>

    <insert id="insertPermissionAndSiteNodeId" parameterType="java.util.List">
        <foreach collection='list' item="item">
        insert into tpl_aclentry_t
        (<include refid="allFields"/>)
        values
        (
        nextval('tpl_aclentry_t_s'),
        #{item.roleId,jdbcType=NUMERIC},
        #{item.permissionId,jdbcType=NUMERIC},
        #{item.resourceType,jdbcType=VARCHAR},
        #{item.siteNodeId,jdbcType=NUMERIC},
        now(),
        #{item.createdBy,jdbcType=NUMERIC},
        now());
        </foreach>
    </insert>


</mapper>