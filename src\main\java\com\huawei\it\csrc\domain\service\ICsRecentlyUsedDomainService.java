/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.domain.service;

import com.huawei.it.csrc.domain.entity.CsRecentlyUsedEntity;

import java.util.List;

/**
 * 最近使用功能领域层
 *
 * <AUTHOR>
 * @since 2022-12-13 10:19:04
 */
public interface ICsRecentlyUsedDomainService {
    /**
     * 最近使用功能列表查询
     *
     * @param csRecentlyUsedEntity csRecentlyUsedEntity
     * @return List<CsRecentlyUsedEntity>
     */
    List<CsRecentlyUsedEntity> findByField(CsRecentlyUsedEntity csRecentlyUsedEntity);

    /**
     * 最近使用功能新增
     *
     * @param csRecentlyUsedEntity csRecentlyUsedEntity
     * @return void
     */
    void save(CsRecentlyUsedEntity csRecentlyUsedEntity);

    /**
     * 获取用户数据集
     *
     * @return string
     */
    List<String> getRoleDataAccess();
}
