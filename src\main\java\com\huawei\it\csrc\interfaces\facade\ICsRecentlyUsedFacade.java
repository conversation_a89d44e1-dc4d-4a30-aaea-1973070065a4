/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.interfaces.facade;

import com.huawei.it.csrc.application.dto.CsRecentlyUsedInputDTO;
import com.huawei.it.csrc.application.dto.CsRecentlyUsedOutputDTO;
import com.huawei.it.csrc.application.dto.CsRecentlyUsedSaveDTO;
import com.huawei.it.csrc.application.dto.MessageResultDTO;

import java.util.List;

import org.springframework.web.bind.annotation.RequestBody;

import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * 最近使用功能接口
 *
 * <AUTHOR>
 * @since 2022-12-13 10:19:04
 */
@Path("/csRecentlyUsed")
@Produces(MediaType.APPLICATION_JSON)
public interface ICsRecentlyUsedFacade {
    /**
     * 最近使用功能列表查询
     *
     * @param csRecentlyUsedInputDto csRecentlyUsedInputDto
     * @return MessageResultDTO<List < CsRecentlyUsedOutputDTO>>
     */
    @POST
    @Path("/menus/findByField")
    MessageResultDTO<List<CsRecentlyUsedOutputDTO>> findByField(
        @RequestBody CsRecentlyUsedInputDTO csRecentlyUsedInputDto);

    /**
     * 新增最近使用功能
     *
     * @param csRecentlyUsedSaveDto csRecentlyUsedSaveDto
     * @return MessageResultDTO
     */
    @POST
    @Path("/menus")
    MessageResultDTO save(@RequestBody CsRecentlyUsedSaveDTO csRecentlyUsedSaveDto);


    /**
     * 获取用户数据集
     *
     * @return string
     */
    @GET
    @Path("/getRoleData")
    List<String> getRoleDataAccess();
}