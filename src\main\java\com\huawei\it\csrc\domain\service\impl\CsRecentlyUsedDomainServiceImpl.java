/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.it.csrc.domain.service.impl;

import com.huawei.it.csrc.domain.entity.CsRecentlyUsedEntity;
import com.huawei.it.csrc.domain.repository.ICsRecentlyUsedRepo;
import com.huawei.it.csrc.domain.service.ICsRecentlyUsedDomainService;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

/**
 * 最近使用功能领域层实现
 *
 * <AUTHOR>
 * @since 2022-12-13 10:19:04
 */
@Named
public class CsRecentlyUsedDomainServiceImpl implements ICsRecentlyUsedDomainService {
    @Inject
    private ICsRecentlyUsedRepo csRecentlyUsedRepo;

    /**
     * 最近使用功能列表查询
     *
     * @param csRecentlyUsedEntity csRecentlyUsedEntity
     * @return List<CsRecentlyUsedEntity>
     */
    @Override
    public List<CsRecentlyUsedEntity> findByField(CsRecentlyUsedEntity csRecentlyUsedEntity) {
        return csRecentlyUsedRepo.findByField(csRecentlyUsedEntity);
    }

    /**
     * 新增最近使用功能
     *
     * @param csRecentlyUsedEntity csRecentlyUsedEntity
     * @return void
     */
    @Override
    public void save(CsRecentlyUsedEntity csRecentlyUsedEntity) {
        csRecentlyUsedRepo.save(csRecentlyUsedEntity);
    }

    @Override
    public List<String> getRoleDataAccess() {
        return csRecentlyUsedRepo.getRoleDataAccess();
    }
}
