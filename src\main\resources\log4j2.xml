<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="60" packages="com.huawei.his.framework.gateway.log.layout">

    <properties>
        <property name="logPath">/applog/cs/logs</property><!-- 日志输出目录 /applog/${application.subAppId}/logs-->
        <property name="every_file_size">50M</property><!-- 日志切割的最小单位 -->
        <property name="output_log_level">info</property><!-- 日志输出级别 -->
    </properties>

    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <JalorPatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss} [%t] %-5p %c{1}:%L - %enc{%msg}{CRLF}%n"/>
        </Console>

        <RollingFile name="root" filename="${logPath}/root.log"
                     filepattern="${logPath}/%d{yyyyMMdd}-%i-root.log" filePermissions="rw-r-----">
            <JalorPatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5p %c{1}:%L - %enc{%msg}{CRLF}%n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="${sys:every_file_size}"/>
            </Policies>
            <DefaultRolloverStrategy max="100" stopCustomActionsOnError="true">
                <PosixViewAttribute basePath="${sys:logPath}" filePermissions="r--r-----">
                    <IfFileName glob="*.log"/>
                </PosixViewAttribute>
            </DefaultRolloverStrategy>
        </RollingFile>

        <RollingFile name="root-error" filename="${logPath}/root-error.log"
                     filepattern="${logPath}/%d{yyyyMMdd}-%i-root-error.log" filePermissions="rw-r-----">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            <JalorPatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5p %c{1}:%L - %enc{%msg}{CRLF}%n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="${sys:every_file_size}"/>
            </Policies>
			<DefaultRolloverStrategy max="100" stopCustomActionsOnError="true">
				<PosixViewAttribute basePath="${sys:logPath}" filePermissions="r--r-----">
					<IfFileName glob="*.log"/>
				</PosixViewAttribute>
			</DefaultRolloverStrategy>
        </RollingFile>

        <RollingFile name="app" filename="${logPath}/app.log"
                     filepattern="${logPath}/%d{yyyyMMdd}-%i-app.log" filePermissions="rw-r-----">
            <JalorPatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5p %c{1}:%L - %enc{%msg}{CRLF}%n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="${sys:every_file_size}"/>
            </Policies>
			<DefaultRolloverStrategy max="100" stopCustomActionsOnError="true">
				<PosixViewAttribute basePath="${sys:logPath}" filePermissions="r--r-----">
					<IfFileName glob="*.log"/>
				</PosixViewAttribute>
			</DefaultRolloverStrategy>
        </RollingFile>

        <RollingFile name="sql" filename="${logPath}/sql.log"
                     filepattern="${logPath}/%d{yyyyMMdd}-%i-sql.log" filePermissions="rw-r-----">
            <JalorPatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5p %c{1}:%L - %enc{%msg}{CRLF}%n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="${sys:every_file_size}"/>
            </Policies>
			<DefaultRolloverStrategy max="100" stopCustomActionsOnError="true">
				<PosixViewAttribute basePath="${sys:logPath}" filePermissions="r--r-----">
					<IfFileName glob="*.log"/>
				</PosixViewAttribute>
			</DefaultRolloverStrategy>
        </RollingFile>

        <RollingFile name="jalor" filename="${logPath}/jalor.log"
                     filepattern="${logPath}/%d{yyyyMMdd}-%i-jalor.log" filePermissions="rw-r-----">
            <JalorPatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5p %c{1}:%L - %enc{%msg}{CRLF}%n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="${sys:every_file_size}"/>
            </Policies>
			<DefaultRolloverStrategy max="100" stopCustomActionsOnError="true">
				<PosixViewAttribute basePath="${sys:logPath}" filePermissions="r--r-----">
					<IfFileName glob="*.log"/>
				</PosixViewAttribute>
			</DefaultRolloverStrategy>
        </RollingFile>

        <!-- tracer输出文件路径配置 -->
        <RollingFile name="app_trace" fileName="${sys:logPath}/jalor_meta.log"
                     filePattern="${sys:logPath}/jalor_meta_%d{yyyy-MM-dd}.%i.log.gz" filePermissions="rw-r-----">
            <Filters>
                <ThresholdFilter level="trace" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <!-- 输出格式 -->
            <JalorPatternLayout
                    pattern="%d{yyyy-MM-dd HH:mm:ss.SSS z} [%thread] %-5level %class{36}.%M()/%L - %enc{%msg}{CRLF}%xEx%n"/>
            <Policies>
                <!-- 每1天更新一次 -->
                <!-- TimeBasedTriggeringPolicy需要和filePattern配套使用，由于filePattern配置的时间最小粒度是dd天，所以表示每一天新建一个文件保存日志。-->
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
                <!-- 此处为每个文件大小策略限制，使用它一般会在文件中filePattern采用%i模式 -->
                <SizeBasedTriggeringPolicy size="${sys:every_file_size}"/>
            </Policies>
			<DefaultRolloverStrategy max="100" stopCustomActionsOnError="true">
				<PosixViewAttribute basePath="${sys:logPath}" filePermissions="r--r-----">
					<IfFileName glob="*.log"/>
				</PosixViewAttribute>
			</DefaultRolloverStrategy>
        </RollingFile>

        <!-- filename : audit日志记录位置, filepattern : 文件大小到达指定尺寸的时候产生一个新的文件 -->
        <!-- ${logPath}：日志文件目录地址 -->
        <!--  jalor-audit.log，%d{yyyyMMdd}-%i-jalor-audit.log：日志文件名称，可根据项目需求修改名称 -->
        <RollingFile name="audit" filename="${logPath}/jalor-audit.log"  filepattern="${logPath}/%d{yyyyMMdd}-%i-jalor-audit.log">
            <JalorPatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5p %c{1}:%L - %enc{%msg}{CRLF}%n" />
            <Policies>
                <SizeBasedTriggeringPolicy size="${sys:every_file_size}"/>
            </Policies>
            <DefaultRolloverStrategy max="100"/>
        </RollingFile>
    </Appenders>
    <Loggers>

        <Logger name="com.huawei.it.jalor5" level="info" additivity="true">
            <AppenderRef ref="jalor"/>
        </Logger>

        <Logger name="com.huawei.it" level="info" additivity="true">
            <AppenderRef ref="app"/>
        </Logger>

        <Logger name="java.sql.Connection" level="info" additivity="true">
            <AppenderRef ref="sql"/>
        </Logger>

        <Logger name="java.sql.PreparedStatement" level="info" additivity="true">
            <AppenderRef ref="sql"/>
        </Logger>

        <Logger name="java.sql.ResultSet" level="info" additivity="true">
            <AppenderRef ref="sql"/>
        </Logger>

        <Logger name="org.springframework.jdbc.datasource.DataSourceTransactionManager" level="info" additivity="true">
            <AppenderRef ref="sql"/>
        </Logger>

        <Logger name="org.springframework.transaction.jta.WebSphereUowTransactionManager" level="info"
                additivity="false">
            <AppenderRef ref="sql"/>
        </Logger>

        <!-- ClasspathLoggingApplicationListener 打印 classpath jar 包 -->
        <Logger name="org.springframework.boot.logging" level="info" additivity="false">
            <AppenderRef ref="sql"/>
        </Logger>


        <Logger name="java.sql.Statement" level="info" additivity="true">
            <AppenderRef ref="sql"/>
        </Logger>

        <Logger name="org.apache.ibatis" level="info" additivity="true">
            <AppenderRef ref="sql"/>
        </Logger>

        <Logger name="org.mybatis.spring" level="info" additivity="true">
            <AppenderRef ref="sql"/>
        </Logger>

        <Logger name="org.springframework" level="info" additivity="true">
        </Logger>

        <Logger name="org.apache.commons" level="info" additivity="true">
        </Logger>

        <Logger name="org.apache.velocity" level="info" additivity="true">
        </Logger>

        <Logger name="org.apache.cxf.configuration.spring" level="info" additivity="true">
        </Logger>

        <Logger name="com.opensymphony.xwork2" level="info" additivity="true">
        </Logger>

        <Logger name="net.sf.ehcache" level="info" additivity="true">
        </Logger>

        <Logger name="org.apache.cxf" level="info" additivity="true">
        </Logger>
        <Logger name="httpclient.wire" level="info" additivity="true">
        </Logger>
        <Logger name="org.apache.http" level="info" additivity="true">
        </Logger>
        <Logger name="com.amazonaws" level="info" additivity="true">
        </Logger>

        <Logger name="org.apache.commons.httpclient.HttpMethodDirector" level="warn" additivity="true">
        </Logger>

        <Logger name="com.huawei.it.jalor5.vega.service.JalorRestClient" level="info" additivity="true">
        </Logger>

        <Logger name="com.huawei.it.jalor5.vegahsa.client.rpc.annotation" level="info" additivity="true">
        </Logger>

        <Logger name="org.springframework.beans.factory.support.DefaultListableBeanFactory" level="error"
                additivity="true">
        </Logger>

        <!-- tracer日志输出到指定文件 -->
        <Logger name="com.huawei.it.usf.tracer.sleuth.report" level="trace" additivity="true">
            <AppenderRef ref="app_trace"/>
        </Logger>

        <Logger name="com.huawei.it.usf.client" level="info" additivity="true">
        </Logger>

        <Logger name="com.huawei.it.jalor.boot" level="info" additivity="true"/>

        <Logger name="org.apache.cxf" level="info" additivity="true"/>

        <Logger name="AuditLogger" level="info" additivity="false">
            <AppenderRef ref="audit"/>
        </Logger>

        <!-- 上线时，应该将各个info改为info以上级别，并去除console的Appender -->
        <Root level="info">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="root"/>
            <AppenderRef ref="root-error"/>
        </Root>
    </Loggers>
</Configuration>