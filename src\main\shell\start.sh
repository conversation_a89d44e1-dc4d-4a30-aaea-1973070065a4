#!/bin/bash
# Copyright (c) Huawei Technologies Co., Ltd. 2010-2022. All rights reserved.
set -ex
echo "Server starting........................"
bin_path=$(dirname $0)
cd $bin_path
bin_path=$(pwd)

push_home=$(cd ../;pwd)
echo $push_home

for i in $(ls $push_home/libs)
	do
		JAVA_OPTIONS=$push_home/libs/$i
	done

echo "$JAVA_OPTIONS"

	REST_PORT=8003
	DSF_PORT=7003
	export REST_PORT DSF_PORT
    java $JAVA_OPTS -XX:MaxMetaspaceSize=512m $CATALINA_OPTS -Denv.prop=$1 -jar $JAVA_OPTIONS

echo "Rest Express Server started........................"