<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <artifactId>fsp-common-service</artifactId>
    <groupId>com.huawei.fsp</groupId>
    <version>1.0-SNAPSHOT</version>
    <name>fsp-common-service</name>

    <parent>
        <groupId>com.huawei.his.framework</groupId>
        <artifactId>jalor-dependencies</artifactId>
        <version>7.0.2.6-SP1.RELEASE</version>
    </parent>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <postgre.version>42.7.2</postgre.version>
        <gateway.version>7.0.2.6.RELEASE</gateway.version>
        <cxf.version>3.5.9</cxf.version>
        <spring.version>5.3.39-h3</spring.version>
        <tomcat.version>9.0.90</tomcat.version>
        <jettison.version>1.5.4</jettison.version>
        <spring-boot.version>2.7.18</spring-boot.version>
        <netty.version>4.1.108.Final</netty.version>
        <jalor-permission.version>7.0.2.6-SP3.RELEASE</jalor-permission.version>
        <jackson.version>2.15.2</jackson.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-idata-huawei</artifactId>
            <version>7.0.2.8-SP1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-permission</artifactId>
            <version>${jalor-permission.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>jalor-model</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>bcprov-jdk15on</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor-core</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor-security-admin</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor-security-api</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-coyote</artifactId>
            <version>${tomcat.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat</groupId>
                    <artifactId>tomcat-jni</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.tomcat</groupId>
                    <artifactId>tomcat-juli</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.tomcat</groupId>
                    <artifactId>tomcat-util</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-servlet-api</artifactId>
            <version>${tomcat.version}</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.jettison</groupId>
            <artifactId>jettison</artifactId>
            <version>${jettison.version}</version>
            <scope>runtime</scope>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>org.glassfish.jersey</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-websocket</artifactId>
            <version>${tomcat.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>tomcat-annotations-api</artifactId>
                    <groupId>org.apache.tomcat</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-el</artifactId>
            <version>${tomcat.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>${tomcat.version}</version>
        </dependency>
        <!-- 2.5的jar包-->
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>huawei-security-conformable</artifactId>
            <version>2.6.19-RELEASE</version>
        </dependency>
        <!--S3-->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.11.104</version>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-webservice-support-sgov</artifactId>
            <version>${jalor.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>jalor-apiauth-sgov</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-core</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>cryptoutil</artifactId>
                    <groupId>com.huawei.wisecloud.secure</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>authentication-client</artifactId>
                    <groupId>com.huawei.it.commonservice</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-xauth-api</artifactId>
            <version>${jalor.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-orm</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--修复Iauth权限下发问题-->
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-xauth-impl</artifactId>
            <version>7.0.2.6.RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>jalor-saas-platform</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-saas-common</artifactId>
            <version>7.0.2.6-SP1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-security-admin</artifactId>
            <version>7.0.2.6-SP10.RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-model</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-aegis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-core</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>huawei-security-dynamicsalt</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor-cache</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--删除角色-组织之间的关联关系SQL异常-->
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-saas-tenant</artifactId>
            <version>7.0.2.6-SP6.RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-model</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-core</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor-saas-platform</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor-security-admin</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--BUG修复：绑定资源为空保存报错问题-->
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-saas-platform</artifactId>
            <version>7.0.2.6-SP2.RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-orm</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor-security-admin</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-starter-cs</artifactId>
            <version>${jalor.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.codehaus.jettison</groupId>
                    <artifactId>jettison</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-websocket</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-el</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-model</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-config-configcenter</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor-excel-impl</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-orm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-boot</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor-saas-tenant</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor-security-admin</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor-security-api</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>CryptoUtil</artifactId>
                    <groupId>com.huawei.wisecloud.secure</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor5.useronline.api</artifactId>
                    <groupId>com.huawei.it.jalor5</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor5.useronline.impl</artifactId>
                    <groupId>com.huawei.it.jalor5</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor-inspection</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>edm3-client-sdk</artifactId>
                    <groupId>com.huawei.it.edm</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor-web-support</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor-auth-jwt</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor-permission</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 创建J2C 选择的加解密算法是2.6的依赖这些包-->
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>huawei-security-base</artifactId>
            <version>2.6.19-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>huawei-security-compatible</artifactId>
            <version>2.6.19-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>huawei-security-dynamicsalt</artifactId>
            <version>2.6.19-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-starter-excel-cs</artifactId>
            <version>${jalor.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>jalor-excel-impl</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-orm</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 导出的文件上传到S3上-->
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-store-s3</artifactId>
            <version>7.0.2.6.RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-boot</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor-model</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>okhttp</artifactId>
                    <groupId>com.squareup.okhttp3</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-auth-sso</artifactId>
            <version>${jalor.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-model</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-boot</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 新增依赖解决租户侧 loo_up_item无法根据app_name添加的问题 -->
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-saas-web-resource</artifactId>
            <version>7.0.2.6-SP1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-tenant-cs-impl</artifactId>
            <version>7.0.2.6-SP2.RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>jalor-saas-platform</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor-saas-tenant</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor-security-admin</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${postgre.version}</version>
        </dependency>
        <dependency>
            <groupId>com.huawei.dt</groupId>
            <artifactId>dt4j-starter-mockito</artifactId>
            <version>2.0.0</version>
            <scope>test</scope>
        </dependency>

        <!--异步日志-->
        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
            <version>3.4.4</version>
        </dependency>

        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-model</artifactId>
            <version>7.0.2.6-SP9.RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--配置中心组件未关闭的io流-->
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-config-configcenter</artifactId>
            <version>7.0.2.6-SP1.RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-core</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>huawei-security-dynamicsalt</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--jalor-excel组件敏感信息打印处理-->
        <dependency>
            <artifactId>jalor-excel-impl</artifactId>
            <groupId>com.huawei.his.framework</groupId>
            <version>7.0.2.6-SP5.RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-orm</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--jalor-aegis Hmac256类encrypt方法缓存不生效-->
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-aegis</artifactId>
            <version>7.0.2.6.RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.huawei.his.framework</groupId>
                    <artifactId>jalor-core</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>CryptoUtil</artifactId>
                    <groupId>com.huawei.wisecloud.secure</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-orm</artifactId>
            <version>7.0.2.6.RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>CryptoUtil</artifactId>
                    <groupId>com.huawei.wisecloud.secure</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>LogFilter</artifactId>
                    <groupId>com.huawei.wisecloud.secure</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>FileUpload</artifactId>
                    <groupId>com.huawei.wisecloud.secure</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor-core</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-boot</artifactId>
            <version>7.0.2.6-SP1.RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>jalor-core</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-gateway-log4j2-security</artifactId>
            <version>${gateway.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.huawei.wisecloud.secure</groupId>
            <artifactId>crypto-util</artifactId>
            <version>1.3.0.300</version>
        </dependency>
        <dependency>
            <groupId>com.huawei.wisecloud.secure</groupId>
            <artifactId>log-filter</artifactId>
            <version>1.3.0.300</version>
        </dependency>
        <dependency>
            <groupId>com.huawei.wisecloud.secure</groupId>
            <artifactId>file-upload</artifactId>
            <version>1.3.0.300</version>
        </dependency>
        <!--【安全加固】限制用户不能同时登录系统-->
        <dependency>
            <groupId>com.huawei.it.jalor5</groupId>
            <artifactId>jalor5.useronline.api</artifactId>
            <version>4.1.5.12</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.huawei.it.jalor5</groupId>
            <artifactId>jalor5.useronline.impl</artifactId>
            <version>4.1.5.12</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-cache</artifactId>
            <version>7.0.2.6.RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>jalor-core</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.huawei.it.commonservice</groupId>
            <artifactId>authentication-client</artifactId>
            <version>2.5.1.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>cxf-core</artifactId>
                    <groupId>org.apache.cxf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- iexcel sdk 版本升级-->
        <dependency>
            <groupId>com.huawei.it.edm</groupId>
            <artifactId>edm3-client-sdk</artifactId>
            <version>3.2.3.6</version>
            <exclusions>
                <exclusion>
                    <artifactId>druid-spring-boot-starter</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>CryptoUtil</artifactId>
                    <groupId>com.huawei.wisecloud.secure</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>okhttp</artifactId>
                    <groupId>com.squareup.okhttp3</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-gateway-udp</artifactId>
            <version>1.1</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.12.269</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.11.0</version>
        </dependency>
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-core</artifactId>
            <version>7.0.2.6-SP15.RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>bcprov-jdk15on</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-web-support</artifactId>
            <version>7.0.2.6-SP5.RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>jalor-boot</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor-api</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-auth-jwt</artifactId>
            <version>7.0.2.6.RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>jalor-boot</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jalor-core</artifactId>
                    <groupId>com.huawei.his.framework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-role-mapping</artifactId>
                <version>7.0.2.6-SP1.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-web</artifactId>
                <version>7.0.2.6-SP1.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-https</artifactId>
                <version>7.0.2.6-SP1.RELEASE</version>
            </dependency>
            <!-- 权限拦截器中使用ApplicationConfigProperties.getContextProperty("xxx") 获取配置调整为 @Value 设置变量获取配置  -->
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-authorization</artifactId>
                <version>7.0.2.6-SP1.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-idata-huawei</artifactId>
                <version>7.0.2.6-SP2.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-idata</artifactId>
                <version>7.0.2.6-SP1.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-helper</artifactId>
                <version>7.0.2.6-SP4.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-download</artifactId>
                <version>7.0.2.6-SP2.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-login</artifactId>
                <version>7.0.2.6-SP1.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-api</artifactId>
                <version>7.0.2.6-SP6.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-validator</artifactId>
                <version>7.0.2.6-SP2.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-i18n-admin</artifactId>
                <version>7.0.2.6-SP3.RELEASE</version>
            </dependency>
            <!--bcprov-jdk18on-->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>1.78.1</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-properties</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <!--容错时请求灰度标识为null，会路由到正式环境问题修复-->
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-loadbalancer</artifactId>
                <version>7.0.2.6.RELEASE</version>
            </dependency>
            <!--BUG修复：使用红版jalor-idata-huawei组件人员联想查询信息不完全，构建用户信息不完整-->
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-user-hw</artifactId>
                <version>7.0.2.6-SP9.RELEASE</version>
            </dependency>
            <!--安全补丁：SimpleClientHttpSSLRequestFactory使用了不安全随机数-->
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-usf-client</artifactId>
                <version>7.0.2.6-SP1.RELEASE</version>
            </dependency>
            <!--BUG修复：jwt认证查找角色异常-->
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-auth-jwt</artifactId>
                <version>7.0.2.6.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>3.5.1</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>3.22.0</version>
            </dependency>
            <dependency>
                <groupId>com.huawei.it.jalor5.hw</groupId>
                <artifactId>huawei.httpdownload</artifactId>
                <version>5.4.15</version>
            </dependency>
            <dependency>
                <groupId>com.huawei.wisecloud.secure</groupId>
                <artifactId>NetPrevention</artifactId>
                <version>1.2.0.309</version>
            </dependency>
            <!-- 添加分布式缓存组件!-->
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-cache-redis</artifactId>
                <version>7.0.1-FT5.RELEASE</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.huawei.his.framework</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-cache</artifactId>
                <version>${jalor.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
                <version>${cxf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-spring-boot-starter-jaxrs</artifactId>
                <version>${cxf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-rt-frontend-jaxrs</artifactId>
                <version>${cxf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.26.1</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.13.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
    </profiles>
    <build>
        <finalName>fsp-common-service</finalName>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.22.2</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <appendAssemblyId>false</appendAssemblyId>
                    <descriptors>
                        <descriptor>src/main/assembly/descriptor.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>2.8</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <properties>
                        <includeTags>junit5</includeTags>
                    </properties>
                    <skipTests>false</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.huawei.dt</groupId>
                <artifactId>dt4j-coverage-maven-plugin</artifactId>
                <version>2.0.0</version>
                <configuration>
                    <!-- 报告名称 -->
                    <reportName>FspCommonServiceReport</reportName>
                    <reportVersion>1.0.0</reportVersion>
                    <reportUser>TestUser</reportUser>
                    <openOnFinish>true</openOnFinish>
                    <!-- 覆盖率包含的包或类路径 -->
                    <includeStats>
                    </includeStats>
                    <!-- 覆盖率排除的包或类路径 -->
                    <excludeStats>
                        <item>com/huawei/it/csrc/base/vo/*</item>
                        <item>com/huawei/it/csrc/infrastructure/*</item>
                        <item>com/huawei/it/csrc/interfaces/facade/*</item>
                        <item>com/huawei/it/csrc/domain/*</item>
                        <item>com/huawei/it/csrc/application/dto/*</item>
                        <item>com/huawei/it/csrc/application/assembler/*</item>
                        <item>com/huawei/it/csrc/application/service/impl/AttachmentAppServiceImpl</item>
                        <item>com/huawei/it/csrc/application/service/impl/CsFavoritesMenuAppServiceImpl</item>
                        <item>com/huawei/it/csrc/CommonServiceApplication</item>
                    </excludeStats>
                    <!-- 检查代码中所执行POM根目录相对于检出根目录的相对目录，自己项目的目录 -->
                    <checkoutRelativeDirectory>opt.fsp.cs.service3</checkoutRelativeDirectory>
                </configuration>
                <executions>
                    <!-- 必须添加instrument任务，否则无法采集到class的指令执行数据 -->
                    <execution>
                        <id>instrument</id>
                        <goals>
                            <goal>instrument</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <showWarnings>true</showWarnings>
                    <compilerArgs>
                        <arg>-Xlint:all</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>